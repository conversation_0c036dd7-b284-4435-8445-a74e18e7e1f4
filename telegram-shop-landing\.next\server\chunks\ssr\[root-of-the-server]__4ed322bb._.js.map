{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_e14c74cb.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_e14c74cb-module__CpULYq__className\",\n  \"variable\": \"inter_e14c74cb-module__CpULYq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 11, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_e14c74cb.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22,%22cyrillic%22],%22variable%22:%22--font-inter%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,gKAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,gKAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,gKAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/src/app/layout.tsx"], "sourcesContent": ["import type { Metada<PERSON> } from \"next\";\nimport { Inter } from \"next/font/google\";\nimport \"./globals.css\";\nimport FloatingParticles from \"@/components/FloatingParticles\";\n\nconst inter = Inter({\n  subsets: [\"latin\", \"cyrillic\"],\n  variable: \"--font-inter\",\n});\n\nexport const metadata: Metadata = {\n  title: \"TeleShop Pro - Создайте магазин в Telegram за 15 минут\",\n  description: \"Современная платформа для создания профессиональных интернет-магазинов в Telegram. Больше продаж, меньше затрат, максимум автоматизации.\",\n  keywords: \"telegram магазин, интернет-магазин, telegram bot, продажи в telegram, создать магазин\",\n  authors: [{ name: \"TeleShop Pro Team\" }],\n  openGraph: {\n    title: \"TeleShop Pro - Создайте магазин в Telegram за 15 минут\",\n    description: \"Современная платформа для создания профессиональных интернет-магазинов в Telegram\",\n    type: \"website\",\n    locale: \"ru_RU\",\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: \"TeleShop Pro - Создайте магазин в Telegram за 15 минут\",\n    description: \"Современная платформа для создания профессиональных интернет-магазинов в Telegram\",\n  },\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"ru\" className=\"scroll-smooth\">\n      <body className={`${inter.variable} antialiased`}>\n        {children}\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAUO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;IACV,SAAS;QAAC;YAAE,MAAM;QAAoB;KAAE;IACxC,WAAW;QACT,OAAO;QACP,aAAa;QACb,MAAM;QACN,QAAQ;IACV;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;IACf;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAK,WAAU;kBACxB,cAAA,8OAAC;YAAK,WAAW,GAAG,oJAAK,CAAC,QAAQ,CAAC,YAAY,CAAC;sBAC7C;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}