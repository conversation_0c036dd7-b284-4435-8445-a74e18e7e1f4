import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import FloatingParticles from "@/components/FloatingParticles";
import CustomCursor from "@/components/CustomCursor";
import ScrollProgress from "@/components/ScrollProgress";

const inter = Inter({
  subsets: ["latin", "cyrillic"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "TeleShop Pro - Создайте магазин в Telegram за 15 минут",
  description: "Современная платформа для создания профессиональных интернет-магазинов в Telegram. Больше продаж, меньше затрат, максимум автоматизации.",
  keywords: "telegram магазин, интернет-магазин, telegram bot, продажи в telegram, создать магазин",
  authors: [{ name: "TeleShop Pro Team" }],
  openGraph: {
    title: "TeleShop Pro - Создайте магазин в Telegram за 15 минут",
    description: "Современная платформа для создания профессиональных интернет-магазинов в Telegram",
    type: "website",
    locale: "ru_RU",
  },
  twitter: {
    card: "summary_large_image",
    title: "TeleShop Pro - Создайте магазин в Telegram за 15 минут",
    description: "Современная платформа для создания профессиональных интернет-магазинов в Telegram",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ru" className="scroll-smooth">
      <body className={`${inter.variable} antialiased`}>
        <ScrollProgress />
        <FloatingParticles />
        <CustomCursor />
        {children}
      </body>
    </html>
  );
}
