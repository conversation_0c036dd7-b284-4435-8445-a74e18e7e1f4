{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/src/components/FloatingParticles.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useRef } from 'react'\nimport { motion } from 'framer-motion'\n\ninterface Particle {\n  id: number\n  x: number\n  y: number\n  size: number\n  speedX: number\n  speedY: number\n  opacity: number\n}\n\nconst FloatingParticles = () => {\n  const containerRef = useRef<HTMLDivElement>(null)\n  const particlesRef = useRef<Particle[]>([])\n  const animationRef = useRef<number>()\n\n  useEffect(() => {\n    const container = containerRef.current\n    if (!container) return\n\n    // Initialize particles\n    const particleCount = 50\n    particlesRef.current = Array.from({ length: particleCount }, (_, i) => ({\n      id: i,\n      x: Math.random() * window.innerWidth,\n      y: Math.random() * window.innerHeight,\n      size: Math.random() * 3 + 1,\n      speedX: (Math.random() - 0.5) * 0.5,\n      speedY: (Math.random() - 0.5) * 0.5,\n      opacity: Math.random() * 0.5 + 0.1,\n    }))\n\n    const animate = () => {\n      particlesRef.current.forEach(particle => {\n        particle.x += particle.speedX\n        particle.y += particle.speedY\n\n        // Wrap around screen\n        if (particle.x > window.innerWidth) particle.x = 0\n        if (particle.x < 0) particle.x = window.innerWidth\n        if (particle.y > window.innerHeight) particle.y = 0\n        if (particle.y < 0) particle.y = window.innerHeight\n      })\n\n      animationRef.current = requestAnimationFrame(animate)\n    }\n\n    animate()\n\n    return () => {\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current)\n      }\n    }\n  }, [])\n\n  return (\n    <div ref={containerRef} className=\"fixed inset-0 pointer-events-none z-0\">\n      {particlesRef.current.map(particle => (\n        <motion.div\n          key={particle.id}\n          className=\"absolute bg-white/20 rounded-full\"\n          style={{\n            width: particle.size,\n            height: particle.size,\n            left: particle.x,\n            top: particle.y,\n            opacity: particle.opacity,\n          }}\n          animate={{\n            scale: [1, 1.2, 1],\n            opacity: [particle.opacity, particle.opacity * 0.5, particle.opacity],\n          }}\n          transition={{\n            duration: 3 + Math.random() * 2,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n          }}\n        />\n      ))}\n    </div>\n  )\n}\n\nexport default FloatingParticles\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAeA,MAAM,oBAAoB;IACxB,MAAM,eAAe,IAAA,+MAAM,EAAiB;IAC5C,MAAM,eAAe,IAAA,+MAAM,EAAa,EAAE;IAC1C,MAAM,eAAe,IAAA,+MAAM;IAE3B,IAAA,kNAAS,EAAC;QACR,MAAM,YAAY,aAAa,OAAO;QACtC,IAAI,CAAC,WAAW;QAEhB,uBAAuB;QACvB,MAAM,gBAAgB;QACtB,aAAa,OAAO,GAAG,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAc,GAAG,CAAC,GAAG,IAAM,CAAC;gBACtE,IAAI;gBACJ,GAAG,KAAK,MAAM,KAAK,OAAO,UAAU;gBACpC,GAAG,KAAK,MAAM,KAAK,OAAO,WAAW;gBACrC,MAAM,KAAK,MAAM,KAAK,IAAI;gBAC1B,QAAQ,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAChC,QAAQ,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAChC,SAAS,KAAK,MAAM,KAAK,MAAM;YACjC,CAAC;QAED,MAAM,UAAU;YACd,aAAa,OAAO,CAAC,OAAO,CAAC,CAAA;gBAC3B,SAAS,CAAC,IAAI,SAAS,MAAM;gBAC7B,SAAS,CAAC,IAAI,SAAS,MAAM;gBAE7B,qBAAqB;gBACrB,IAAI,SAAS,CAAC,GAAG,OAAO,UAAU,EAAE,SAAS,CAAC,GAAG;gBACjD,IAAI,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG,OAAO,UAAU;gBAClD,IAAI,SAAS,CAAC,GAAG,OAAO,WAAW,EAAE,SAAS,CAAC,GAAG;gBAClD,IAAI,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG,OAAO,WAAW;YACrD;YAEA,aAAa,OAAO,GAAG,sBAAsB;QAC/C;QAEA;QAEA,OAAO;YACL,IAAI,aAAa,OAAO,EAAE;gBACxB,qBAAqB,aAAa,OAAO;YAC3C;QACF;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,KAAK;QAAc,WAAU;kBAC/B,aAAa,OAAO,CAAC,GAAG,CAAC,CAAA,yBACxB,8OAAC,oMAAM,CAAC,GAAG;gBAET,WAAU;gBACV,OAAO;oBACL,OAAO,SAAS,IAAI;oBACpB,QAAQ,SAAS,IAAI;oBACrB,MAAM,SAAS,CAAC;oBAChB,KAAK,SAAS,CAAC;oBACf,SAAS,SAAS,OAAO;gBAC3B;gBACA,SAAS;oBACP,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;oBAClB,SAAS;wBAAC,SAAS,OAAO;wBAAE,SAAS,OAAO,GAAG;wBAAK,SAAS,OAAO;qBAAC;gBACvE;gBACA,YAAY;oBACV,UAAU,IAAI,KAAK,MAAM,KAAK;oBAC9B,QAAQ;oBACR,MAAM;gBACR;eAjBK,SAAS,EAAE;;;;;;;;;;AAsB1B;uCAEe", "debugId": null}}]}