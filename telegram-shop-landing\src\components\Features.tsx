'use client'

import { motion, useScroll, useTransform } from 'framer-motion'
import { useRef } from 'react'
import AnimatedCounter from '@/components/AnimatedCounter'
import { 
  Smartphone, 
  Zap, 
  Shield, 
  TrendingUp, 
  Users, 
  Settings,
  CreditCard,
  BarChart3,
  Palette,
  Globe
} from 'lucide-react'

const Features = () => {
  const containerRef = useRef<HTMLDivElement>(null)
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  })

  const backgroundY = useTransform(scrollYProgress, [0, 1], [0, -100])
  const features = [
    {
      icon: Smartphone,
      title: 'Нативный Telegram опыт',
      description: 'Магазин интегрируется прямо в Telegram без переходов на внешние сайты',
      color: 'from-blue-400 to-blue-600',
      delay: 0
    },
    {
      icon: Zap,
      title: 'Запуск за 15 минут',
      description: 'Простая настройка без технических знаний. Загрузите товары и начинайте продавать',
      color: 'from-yellow-400 to-orange-500',
      delay: 0.1
    },
    {
      icon: TrendingUp,
      title: 'Высокая конверсия',
      description: 'До 21% конверсии против 2% на обычных сайтах благодаря удобству Telegram',
      color: 'from-green-400 to-green-600',
      delay: 0.2
    },
    {
      icon: CreditCard,
      title: 'Множество способов оплаты',
      description: 'TON, Telegram Payments, банковские карты и криптовалюты',
      color: 'from-purple-400 to-purple-600',
      delay: 0.3
    },
    {
      icon: Palette,
      title: 'Кастомный дизайн',
      description: 'Полная настройка внешнего вида под ваш бренд с анимациями и эффектами',
      color: 'from-pink-400 to-rose-500',
      delay: 0.4
    },
    {
      icon: BarChart3,
      title: 'Аналитика и отчёты',
      description: 'Детальная статистика продаж, поведения клиентов и эффективности',
      color: 'from-indigo-400 to-indigo-600',
      delay: 0.5
    },
    {
      icon: Users,
      title: 'CRM система',
      description: 'Управление клиентами, заказами и автоматические уведомления',
      color: 'from-teal-400 to-teal-600',
      delay: 0.6
    },
    {
      icon: Shield,
      title: 'Безопасность',
      description: 'Защищённые платежи и данные клиентов с шифрованием',
      color: 'from-red-400 to-red-600',
      delay: 0.7
    },
    {
      icon: Settings,
      title: 'Автоматизация',
      description: 'Автоматическое обновление остатков, цен и обработка заказов',
      color: 'from-gray-400 to-gray-600',
      delay: 0.8
    },
    {
      icon: Globe,
      title: 'Мультиязычность',
      description: 'Поддержка множества языков для международных продаж',
      color: 'from-cyan-400 to-cyan-600',
      delay: 0.9
    }
  ]

  return (
    <section ref={containerRef} id="features" className="py-20 lg:py-32 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0">
        <motion.div
          style={{ y: backgroundY }}
          className="absolute top-1/4 right-0 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"
        />
        <motion.div
          style={{ y: backgroundY }}
          className="absolute bottom-1/4 left-0 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"
        />
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <h2 className="text-4xl lg:text-6xl font-bold mb-6">
            Почему выбирают{' '}
            <span className="gradient-text">нас</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Современные технологии и проверенные решения для максимального роста вашего бизнеса
          </p>
        </motion.div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: feature.delay }}
              viewport={{ once: true }}
              whileHover={{ 
                y: -10, 
                scale: 1.05,
                transition: { duration: 0.2 }
              }}
              className={`glass rounded-2xl p-6 group hover:bg-white/10 transition-all duration-300 ${
                index < 5 ? 'lg:col-span-1' : 'md:col-span-1'
              }`}
            >
              {/* Icon */}
              <motion.div
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.6 }}
                className={`inline-flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-r ${feature.color} mb-4 group-hover:shadow-lg transition-all duration-300`}
              >
                <feature.icon className="w-6 h-6 text-white" />
              </motion.div>

              {/* Content */}
              <h3 className="text-lg font-semibold text-white mb-3 group-hover:text-blue-300 transition-colors">
                {feature.title}
              </h3>
              <p className="text-gray-400 text-sm leading-relaxed group-hover:text-gray-300 transition-colors">
                {feature.description}
              </p>

              {/* Hover Effect */}
              <div className={`absolute inset-0 rounded-2xl bg-gradient-to-r ${feature.color} opacity-0 group-hover:opacity-5 transition-opacity duration-300`}></div>
            </motion.div>
          ))}
        </div>

        {/* Bottom Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto"
        >
          {[
            { value: 2000, suffix: '+', label: 'Активных магазинов' },
            { value: 150, suffix: '%', label: 'Средний рост продаж' },
            { value: 15, suffix: ' мин', label: 'Время запуска' },
            { value: 24, suffix: '/7', label: 'Поддержка клиентов' }
          ].map((stat, index) => (
            <motion.div
              key={index}
              whileHover={{ scale: 1.05 }}
              className="text-center group"
            >
              <div className="text-3xl lg:text-4xl font-bold gradient-text mb-2 group-hover:scale-110 transition-transform">
                <AnimatedCounter
                  end={stat.value}
                  suffix={stat.suffix}
                />
              </div>
              <div className="text-gray-400 text-sm">{stat.label}</div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}

export default Features
