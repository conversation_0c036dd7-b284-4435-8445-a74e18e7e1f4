'use client'

import { motion } from 'framer-motion'
import { ExternalLink, Star, TrendingUp, Users, ShoppingBag } from 'lucide-react'
import { Button } from '@/components/ui/button'

const Examples = () => {
  const examples = [
    {
      name: 'Fashion Store',
      category: 'Одежда и аксессуары',
      description: 'Стильный магазин модной одежды с интуитивным каталогом и быстрой оплатой',
      image: '/api/placeholder/400/300',
      stats: {
        sales: '+250%',
        customers: '15K+',
        rating: '4.9'
      },
      features: ['Кастомный дизайн', 'Интеграция с доставкой', 'Автоматические уведомления'],
      color: 'from-pink-500 to-rose-500'
    },
    {
      name: 'Tech Gadgets',
      category: 'Электроника',
      description: 'Магазин гаджетов и электроники с детальными описаниями товаров',
      image: '/api/placeholder/400/300',
      stats: {
        sales: '+180%',
        customers: '8K+',
        rating: '4.8'
      },
      features: ['Сравнение товаров', 'Техническая поддержка', 'Гарантийное обслуживание'],
      color: 'from-blue-500 to-cyan-500'
    },
    {
      name: 'Healthy Food',
      category: 'Здоровое питание',
      description: 'Магазин органических продуктов с системой подписок и доставкой',
      image: '/api/placeholder/400/300',
      stats: {
        sales: '+320%',
        customers: '22K+',
        rating: '4.9'
      },
      features: ['Подписки на товары', 'Календарь доставки', 'Программа лояльности'],
      color: 'from-green-500 to-emerald-500'
    }
  ]

  return (
    <section id="examples" className="py-20 lg:py-32 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-0 right-1/3 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-1/3 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl lg:text-6xl font-bold mb-6">
            Успешные{' '}
            <span className="gradient-text">примеры</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Посмотрите, как наши клиенты создают успешные магазины в Telegram и увеличивают продажи
          </p>
        </motion.div>

        {/* Examples Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
          {examples.map((example, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
              whileHover={{ y: -10, scale: 1.02 }}
              className="glass rounded-3xl overflow-hidden border border-white/10 group"
            >
              {/* Image Placeholder */}
              <div className={`h-48 bg-gradient-to-br ${example.color} relative overflow-hidden`}>
                <div className="absolute inset-0 bg-black/20"></div>
                <div className="absolute top-4 right-4">
                  <div className="glass rounded-full p-2">
                    <ExternalLink className="w-4 h-4 text-white" />
                  </div>
                </div>
                <div className="absolute bottom-4 left-4">
                  <span className="glass px-3 py-1 rounded-full text-sm text-white">
                    {example.category}
                  </span>
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                <h3 className="text-xl font-bold text-white mb-2 group-hover:text-blue-300 transition-colors">
                  {example.name}
                </h3>
                <p className="text-gray-400 text-sm mb-4 leading-relaxed">
                  {example.description}
                </p>

                {/* Stats */}
                <div className="grid grid-cols-3 gap-4 mb-6">
                  <div className="text-center">
                    <div className="flex items-center justify-center mb-1">
                      <TrendingUp className="w-4 h-4 text-green-400 mr-1" />
                      <span className="text-lg font-bold text-white">{example.stats.sales}</span>
                    </div>
                    <span className="text-xs text-gray-400">Рост продаж</span>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center mb-1">
                      <Users className="w-4 h-4 text-blue-400 mr-1" />
                      <span className="text-lg font-bold text-white">{example.stats.customers}</span>
                    </div>
                    <span className="text-xs text-gray-400">Клиентов</span>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center mb-1">
                      <Star className="w-4 h-4 text-yellow-400 mr-1" />
                      <span className="text-lg font-bold text-white">{example.stats.rating}</span>
                    </div>
                    <span className="text-xs text-gray-400">Рейтинг</span>
                  </div>
                </div>

                {/* Features */}
                <div className="space-y-2 mb-6">
                  {example.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-center text-sm text-gray-300">
                      <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mr-2"></div>
                      {feature}
                    </div>
                  ))}
                </div>

                {/* CTA */}
                <Button 
                  variant="outline" 
                  className="w-full border-white/20 hover:border-white/40 text-white hover:bg-white/10 group-hover:scale-105 transition-all duration-300"
                >
                  <ShoppingBag className="w-4 h-4 mr-2" />
                  Посмотреть магазин
                </Button>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Stats Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="glass rounded-3xl p-8 text-center"
        >
          <h3 className="text-2xl font-bold text-white mb-6">
            Результаты наших клиентов
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {[
              { value: '2000+', label: 'Созданных магазинов', icon: ShoppingBag },
              { value: '150%', label: 'Средний рост продаж', icon: TrendingUp },
              { value: '4.9', label: 'Средний рейтинг', icon: Star },
              { value: '95%', label: 'Довольных клиентов', icon: Users }
            ].map((stat, index) => (
              <motion.div
                key={index}
                whileHover={{ scale: 1.05 }}
                className="group"
              >
                <div className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl mb-3 group-hover:shadow-lg transition-all duration-300">
                  <stat.icon className="w-6 h-6 text-white" />
                </div>
                <div className="text-3xl font-bold gradient-text mb-1 group-hover:scale-110 transition-transform">
                  {stat.value}
                </div>
                <div className="text-gray-400 text-sm">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <h3 className="text-2xl font-bold text-white mb-4">
            Готовы создать свой успешный магазин?
          </h3>
          <p className="text-gray-300 mb-8 max-w-2xl mx-auto">
            Присоединяйтесь к тысячам предпринимателей, которые уже увеличили свои продажи с TeleShop Pro
          </p>
          <Button variant="gradient" size="lg" className="shadow-2xl hover:shadow-blue-500/25">
            Создать магазин бесплатно
          </Button>
        </motion.div>
      </div>
    </section>
  )
}

export default Examples
