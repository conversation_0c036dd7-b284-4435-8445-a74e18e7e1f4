{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/src/components/Header.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/src/components/Header.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/src/components/Hero.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Hero.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Hero.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA2R,GACxT,yDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 65, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/src/components/Hero.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Hero.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Hero.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAuQ,GACpS,qCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/src/components/Features.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Features.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Features.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/src/components/Features.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Features.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Features.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA2Q,GACxS,yCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/src/components/ParallaxSection.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ParallaxSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ParallaxSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/src/components/ParallaxSection.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ParallaxSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ParallaxSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAkR,GAC/S,gDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/src/components/Examples.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Examples.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Examples.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/src/components/Examples.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Examples.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Examples.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA2Q,GACxS,yCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/src/components/Pricing.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Pricing.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Pricing.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA8R,GAC3T,4DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/src/components/Pricing.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Pricing.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Pricing.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA0Q,GACvS,wCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 231, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/src/components/FAQ.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/FAQ.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/FAQ.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA0R,GACvT,wDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/src/components/FAQ.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/FAQ.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/FAQ.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAsQ,GACnS,oCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/src/components/Footer.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Footer.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Footer.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 281, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/src/components/Footer.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Footer.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Footer.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 295, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/src/app/page.tsx"], "sourcesContent": ["import Header from '@/components/Header'\nimport Hero from '@/components/Hero'\nimport Features from '@/components/Features'\nimport ParallaxSection from '@/components/ParallaxSection'\nimport Examples from '@/components/Examples'\nimport Pricing from '@/components/Pricing'\nimport FAQ from '@/components/FAQ'\nimport Footer from '@/components/Footer'\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen\">\n      <Header />\n      <main>\n        <Hero />\n        <Features />\n        <ParallaxSection />\n        <Examples />\n        <Pricing />\n        <FAQ />\n      </main>\n      <Footer />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,uIAAM;;;;;0BACP,8OAAC;;kCACC,8OAAC,qIAAI;;;;;kCACL,8OAAC,yIAAQ;;;;;kCACT,8OAAC,gJAAe;;;;;kCAChB,8OAAC,yIAAQ;;;;;kCACT,8OAAC,wIAAO;;;;;kCACR,8OAAC,oIAAG;;;;;;;;;;;0BAEN,8OAAC,uIAAM;;;;;;;;;;;AAGb", "debugId": null}}]}