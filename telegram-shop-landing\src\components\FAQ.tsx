'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Plus, Minus } from 'lucide-react'

const FAQ = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(0)

  const faqs = [
    {
      question: 'Что такое TeleShop Pro и чем он отличается от других сервисов?',
      answer: 'TeleShop Pro — это современная платформа для создания нативных Telegram-магазинов за 15 минут. Мы специализируемся на Telegram Mini App, поэтому интерфейс для ваших клиентов выглядит как покупка на сайте, но быстрее и удобнее. У нас есть весь функционал для e-commerce: интеграции с доставками, управление остатками, аналитика, онлайн-платежи и многое другое.'
    },
    {
      question: 'Могу ли я создать только каталог без онлайн-платежей?',
      answer: 'Да, конечно! В TeleShop Pro можно создать только каталог и принимать заявки без подключения онлайн-платежей. Создайте кастомный способ оплаты (например, оплата курьеру или наличными), и вы будете получать уведомления о заказах для дальнейшей связи с клиентами.'
    },
    {
      question: 'Сколько стоит использование сервиса?',
      answer: 'При регистрации вы получаете 14 дней бесплатного пробного периода с полным доступом ко всем функциям. Далее выберите один из тарифов: Start (149€ + 9€/мес), Pro (249€ + 19€/мес) или Agency (от 499€ + от 49€/мес). TeleShop Pro не берет комиссий с продаж и не требует дополнительных платежей.'
    },
    {
      question: 'Что будет после окончания пробного периода?',
      answer: 'Все данные магазина будут сохранены, но он временно станет недоступен для клиентов, пока вы не оформите подписку. Как только вы выберете тариф, магазин снова заработает со всеми настройками и товарами.'
    },
    {
      question: 'Какие способы оплаты поддерживаются?',
      answer: 'Мы поддерживаем множество способов оплаты: TON, Telegram Payments, банковские карты, криптовалюты, а также возможность создания кастомных способов оплаты для вашего бизнеса.'
    },
    {
      question: 'Можно ли настроить дизайн под свой бренд?',
      answer: 'Да! В тарифах Pro и Agency доступна полная кастомизация дизайна: цвета, шрифты, анимации, логотип и даже кастомная Telegram-тема. Мы поможем создать уникальный внешний вид, который отражает ваш бренд.'
    },
    {
      question: 'Есть ли техническая поддержка?',
      answer: 'Конечно! Мы предоставляем техническую поддержку на всех тарифах. В тарифе Agency доступна приоритетная поддержка 24/7. Наша команда поможет с настройкой, интеграциями и любыми вопросами по использованию платформы.'
    },
    {
      question: 'Можно ли интегрировать магазин с внешними системами?',
      answer: 'Да! Мы предоставляем API для интеграции с внешними системами, CRM, складскими программами и другими сервисами. В тарифе Agency доступны расширенные возможности интеграции и кастомная разработка.'
    }
  ]

  return (
    <section id="faq" className="py-20 lg:py-32 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-1/3 left-1/4 w-96 h-96 bg-green-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/3 right-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl lg:text-6xl font-bold mb-6">
            Часто задаваемые{' '}
            <span className="gradient-text">вопросы</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Ответы на самые популярные вопросы о создании и управлении Telegram-магазинами
          </p>
        </motion.div>

        {/* FAQ Items */}
        <div className="max-w-4xl mx-auto">
          {faqs.map((faq, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="mb-4"
            >
              <motion.div
                whileHover={{ scale: 1.01 }}
                className="glass rounded-2xl border border-white/10 overflow-hidden"
              >
                <button
                  onClick={() => setOpenIndex(openIndex === index ? null : index)}
                  className="w-full px-6 py-6 text-left flex items-center justify-between hover:bg-white/5 transition-colors duration-200"
                >
                  <h3 className="text-lg font-semibold text-white pr-4">
                    {faq.question}
                  </h3>
                  <motion.div
                    animate={{ rotate: openIndex === index ? 45 : 0 }}
                    transition={{ duration: 0.2 }}
                    className="flex-shrink-0"
                  >
                    {openIndex === index ? (
                      <Minus className="w-5 h-5 text-blue-400" />
                    ) : (
                      <Plus className="w-5 h-5 text-gray-400" />
                    )}
                  </motion.div>
                </button>
                
                <AnimatePresence>
                  {openIndex === index && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3, ease: 'easeInOut' }}
                      className="overflow-hidden"
                    >
                      <div className="px-6 pb-6 pt-0">
                        <div className="h-px bg-gradient-to-r from-transparent via-white/20 to-transparent mb-4"></div>
                        <p className="text-gray-300 leading-relaxed">
                          {faq.answer}
                        </p>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="glass rounded-3xl p-8 max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-white mb-4">
              Остались вопросы?
            </h3>
            <p className="text-gray-300 mb-6">
              Наша команда поддержки готова помочь вам 24/7. Свяжитесь с нами любым удобным способом.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-300"
              >
                Написать в поддержку
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-6 py-3 border border-white/20 text-white rounded-lg font-semibold hover:bg-white/10 transition-all duration-300"
              >
                Запланировать звонок
              </motion.button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default FAQ
