{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/src/components/FloatingParticles.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useRef } from 'react'\nimport { motion } from 'framer-motion'\n\ninterface Particle {\n  id: number\n  x: number\n  y: number\n  size: number\n  speedX: number\n  speedY: number\n  opacity: number\n}\n\nconst FloatingParticles = () => {\n  const containerRef = useRef<HTMLDivElement>(null)\n  const particlesRef = useRef<Particle[]>([])\n  const animationRef = useRef<number>()\n\n  useEffect(() => {\n    const container = containerRef.current\n    if (!container) return\n\n    // Initialize particles\n    const particleCount = 50\n    particlesRef.current = Array.from({ length: particleCount }, (_, i) => ({\n      id: i,\n      x: Math.random() * window.innerWidth,\n      y: Math.random() * window.innerHeight,\n      size: Math.random() * 3 + 1,\n      speedX: (Math.random() - 0.5) * 0.5,\n      speedY: (Math.random() - 0.5) * 0.5,\n      opacity: Math.random() * 0.5 + 0.1,\n    }))\n\n    const animate = () => {\n      particlesRef.current.forEach(particle => {\n        particle.x += particle.speedX\n        particle.y += particle.speedY\n\n        // Wrap around screen\n        if (particle.x > window.innerWidth) particle.x = 0\n        if (particle.x < 0) particle.x = window.innerWidth\n        if (particle.y > window.innerHeight) particle.y = 0\n        if (particle.y < 0) particle.y = window.innerHeight\n      })\n\n      animationRef.current = requestAnimationFrame(animate)\n    }\n\n    animate()\n\n    return () => {\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current)\n      }\n    }\n  }, [])\n\n  return (\n    <div ref={containerRef} className=\"fixed inset-0 pointer-events-none z-0\">\n      {particlesRef.current.map(particle => (\n        <motion.div\n          key={particle.id}\n          className=\"absolute bg-white/20 rounded-full\"\n          style={{\n            width: particle.size,\n            height: particle.size,\n            left: particle.x,\n            top: particle.y,\n            opacity: particle.opacity,\n          }}\n          animate={{\n            scale: [1, 1.2, 1],\n            opacity: [particle.opacity, particle.opacity * 0.5, particle.opacity],\n          }}\n          transition={{\n            duration: 3 + Math.random() * 2,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n          }}\n        />\n      ))}\n    </div>\n  )\n}\n\nexport default FloatingParticles\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAeA,MAAM,oBAAoB;;IACxB,MAAM,eAAe,IAAA,uKAAM,EAAiB;IAC5C,MAAM,eAAe,IAAA,uKAAM,EAAa,EAAE;IAC1C,MAAM,eAAe,IAAA,uKAAM;IAE3B,IAAA,0KAAS;uCAAC;YACR,MAAM,YAAY,aAAa,OAAO;YACtC,IAAI,CAAC,WAAW;YAEhB,uBAAuB;YACvB,MAAM,gBAAgB;YACtB,aAAa,OAAO,GAAG,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAc;+CAAG,CAAC,GAAG,IAAM,CAAC;wBACtE,IAAI;wBACJ,GAAG,KAAK,MAAM,KAAK,OAAO,UAAU;wBACpC,GAAG,KAAK,MAAM,KAAK,OAAO,WAAW;wBACrC,MAAM,KAAK,MAAM,KAAK,IAAI;wBAC1B,QAAQ,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;wBAChC,QAAQ,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;wBAChC,SAAS,KAAK,MAAM,KAAK,MAAM;oBACjC,CAAC;;YAED,MAAM;uDAAU;oBACd,aAAa,OAAO,CAAC,OAAO;+DAAC,CAAA;4BAC3B,SAAS,CAAC,IAAI,SAAS,MAAM;4BAC7B,SAAS,CAAC,IAAI,SAAS,MAAM;4BAE7B,qBAAqB;4BACrB,IAAI,SAAS,CAAC,GAAG,OAAO,UAAU,EAAE,SAAS,CAAC,GAAG;4BACjD,IAAI,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG,OAAO,UAAU;4BAClD,IAAI,SAAS,CAAC,GAAG,OAAO,WAAW,EAAE,SAAS,CAAC,GAAG;4BAClD,IAAI,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG,OAAO,WAAW;wBACrD;;oBAEA,aAAa,OAAO,GAAG,sBAAsB;gBAC/C;;YAEA;YAEA;+CAAO;oBACL,IAAI,aAAa,OAAO,EAAE;wBACxB,qBAAqB,aAAa,OAAO;oBAC3C;gBACF;;QACF;sCAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,KAAK;QAAc,WAAU;kBAC/B,aAAa,OAAO,CAAC,GAAG,CAAC,CAAA,yBACxB,6LAAC,uMAAM,CAAC,GAAG;gBAET,WAAU;gBACV,OAAO;oBACL,OAAO,SAAS,IAAI;oBACpB,QAAQ,SAAS,IAAI;oBACrB,MAAM,SAAS,CAAC;oBAChB,KAAK,SAAS,CAAC;oBACf,SAAS,SAAS,OAAO;gBAC3B;gBACA,SAAS;oBACP,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;oBAClB,SAAS;wBAAC,SAAS,OAAO;wBAAE,SAAS,OAAO,GAAG;wBAAK,SAAS,OAAO;qBAAC;gBACvE;gBACA,YAAY;oBACV,UAAU,IAAI,KAAK,MAAM,KAAK;oBAC9B,QAAQ;oBACR,MAAM;gBACR;eAjBK,SAAS,EAAE;;;;;;;;;;AAsB1B;GAvEM;KAAA;uCAyES", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/src/components/CustomCursor.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { motion } from 'framer-motion'\n\nconst CustomCursor = () => {\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })\n  const [isHovering, setIsHovering] = useState(false)\n\n  useEffect(() => {\n    const updateMousePosition = (e: MouseEvent) => {\n      setMousePosition({ x: e.clientX, y: e.clientY })\n    }\n\n    const handleMouseEnter = () => setIsHovering(true)\n    const handleMouseLeave = () => setIsHovering(false)\n\n    // Add event listeners for interactive elements\n    const interactiveElements = document.querySelectorAll('button, a, [role=\"button\"]')\n    \n    interactiveElements.forEach(el => {\n      el.addEventListener('mouseenter', handleMouseEnter)\n      el.addEventListener('mouseleave', handleMouseLeave)\n    })\n\n    window.addEventListener('mousemove', updateMousePosition)\n\n    return () => {\n      window.removeEventListener('mousemove', updateMousePosition)\n      interactiveElements.forEach(el => {\n        el.removeEventListener('mouseenter', handleMouseEnter)\n        el.removeEventListener('mouseleave', handleMouseLeave)\n      })\n    }\n  }, [])\n\n  return (\n    <>\n      {/* Main cursor */}\n      <motion.div\n        className=\"fixed top-0 left-0 w-4 h-4 bg-blue-500 rounded-full pointer-events-none z-50 mix-blend-difference\"\n        animate={{\n          x: mousePosition.x - 8,\n          y: mousePosition.y - 8,\n          scale: isHovering ? 1.5 : 1,\n        }}\n        transition={{\n          type: \"spring\",\n          stiffness: 500,\n          damping: 28,\n        }}\n      />\n      \n      {/* Cursor trail */}\n      <motion.div\n        className=\"fixed top-0 left-0 w-8 h-8 border-2 border-blue-400/50 rounded-full pointer-events-none z-40\"\n        animate={{\n          x: mousePosition.x - 16,\n          y: mousePosition.y - 16,\n          scale: isHovering ? 2 : 1,\n        }}\n        transition={{\n          type: \"spring\",\n          stiffness: 150,\n          damping: 15,\n        }}\n      />\n    </>\n  )\n}\n\nexport default CustomCursor\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,eAAe;;IACnB,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,yKAAQ,EAAC;QAAE,GAAG;QAAG,GAAG;IAAE;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yKAAQ,EAAC;IAE7C,IAAA,0KAAS;kCAAC;YACR,MAAM;8DAAsB,CAAC;oBAC3B,iBAAiB;wBAAE,GAAG,EAAE,OAAO;wBAAE,GAAG,EAAE,OAAO;oBAAC;gBAChD;;YAEA,MAAM;2DAAmB,IAAM,cAAc;;YAC7C,MAAM;2DAAmB,IAAM,cAAc;;YAE7C,+CAA+C;YAC/C,MAAM,sBAAsB,SAAS,gBAAgB,CAAC;YAEtD,oBAAoB,OAAO;0CAAC,CAAA;oBAC1B,GAAG,gBAAgB,CAAC,cAAc;oBAClC,GAAG,gBAAgB,CAAC,cAAc;gBACpC;;YAEA,OAAO,gBAAgB,CAAC,aAAa;YAErC;0CAAO;oBACL,OAAO,mBAAmB,CAAC,aAAa;oBACxC,oBAAoB,OAAO;kDAAC,CAAA;4BAC1B,GAAG,mBAAmB,CAAC,cAAc;4BACrC,GAAG,mBAAmB,CAAC,cAAc;wBACvC;;gBACF;;QACF;iCAAG,EAAE;IAEL,qBACE;;0BAEE,6LAAC,uMAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG,cAAc,CAAC,GAAG;oBACrB,GAAG,cAAc,CAAC,GAAG;oBACrB,OAAO,aAAa,MAAM;gBAC5B;gBACA,YAAY;oBACV,MAAM;oBACN,WAAW;oBACX,SAAS;gBACX;;;;;;0BAIF,6LAAC,uMAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG,cAAc,CAAC,GAAG;oBACrB,GAAG,cAAc,CAAC,GAAG;oBACrB,OAAO,aAAa,IAAI;gBAC1B;gBACA,YAAY;oBACV,MAAM;oBACN,WAAW;oBACX,SAAS;gBACX;;;;;;;;AAIR;GAhEM;KAAA;uCAkES", "debugId": null}}]}