{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,yKAAO,EAAC,IAAA,gJAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n        gradient: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-300\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,IAAA,0KAAG,EACxB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;YACN,UAAU;QACZ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,2KAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,MAAM,OAAO,UAAU,2KAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,IAAA,4HAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/src/components/Header.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { motion } from 'framer-motion'\nimport { Menu, X, Zap } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { cn } from '@/lib/utils'\n\nconst Header = () => {\n  const [isScrolled, setIsScrolled] = useState(false)\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50)\n    }\n    window.addEventListener('scroll', handleScroll)\n    return () => window.removeEventListener('scroll', handleScroll)\n  }, [])\n\n  const navItems = [\n    { name: 'Возможности', href: '#features' },\n    { name: 'Тарифы', href: '#pricing' },\n    { name: 'Примеры', href: '#examples' },\n    { name: 'FAQ', href: '#faq' },\n  ]\n\n  return (\n    <motion.header\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.6, ease: 'easeOut' }}\n      className={cn(\n        'fixed top-0 left-0 right-0 z-50 transition-all duration-300',\n        isScrolled\n          ? 'glass backdrop-blur-md border-b border-white/10'\n          : 'bg-transparent'\n      )}\n    >\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16 lg:h-20\">\n          {/* Logo */}\n          <motion.div\n            whileHover={{ scale: 1.05 }}\n            className=\"flex items-center space-x-2\"\n          >\n            <div className=\"relative\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\">\n                <Zap className=\"w-5 h-5 text-white\" />\n              </div>\n              <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse\"></div>\n            </div>\n            <span className=\"text-xl font-bold gradient-text\">TeleShop Pro</span>\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:flex items-center space-x-8\">\n            {navItems.map((item, index) => (\n              <motion.a\n                key={item.name}\n                href={item.href}\n                initial={{ opacity: 0, y: -20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: index * 0.1 + 0.3 }}\n                className=\"text-gray-300 hover:text-white transition-colors duration-200 relative group\"\n              >\n                {item.name}\n                <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-600 transition-all duration-300 group-hover:w-full\"></span>\n              </motion.a>\n            ))}\n          </nav>\n\n          {/* CTA Buttons */}\n          <div className=\"hidden lg:flex items-center space-x-4\">\n            <Button variant=\"ghost\" className=\"text-gray-300 hover:text-white\">\n              Войти\n            </Button>\n            <Button variant=\"gradient\" className=\"shadow-lg hover:shadow-xl\">\n              Начать бесплатно\n            </Button>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            className=\"lg:hidden p-2 text-gray-300 hover:text-white transition-colors\"\n          >\n            {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}\n          </button>\n        </div>\n\n        {/* Mobile Menu */}\n        <motion.div\n          initial={false}\n          animate={{\n            height: isMobileMenuOpen ? 'auto' : 0,\n            opacity: isMobileMenuOpen ? 1 : 0,\n          }}\n          transition={{ duration: 0.3 }}\n          className=\"lg:hidden overflow-hidden\"\n        >\n          <div className=\"py-4 space-y-4\">\n            {navItems.map((item) => (\n              <a\n                key={item.name}\n                href={item.href}\n                className=\"block text-gray-300 hover:text-white transition-colors duration-200\"\n                onClick={() => setIsMobileMenuOpen(false)}\n              >\n                {item.name}\n              </a>\n            ))}\n            <div className=\"pt-4 space-y-2\">\n              <Button variant=\"ghost\" className=\"w-full text-gray-300 hover:text-white\">\n                Войти\n              </Button>\n              <Button variant=\"gradient\" className=\"w-full\">\n                Начать бесплатно\n              </Button>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </motion.header>\n  )\n}\n\nexport default Header\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;;;AANA;;;;;;AAQA,MAAM,SAAS;;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yKAAQ,EAAC;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,yKAAQ,EAAC;IAEzD,IAAA,0KAAS;4BAAC;YACR,MAAM;iDAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,MAAM,WAAW;QACf;YAAE,MAAM;YAAe,MAAM;QAAY;QACzC;YAAE,MAAM;YAAU,MAAM;QAAW;QACnC;YAAE,MAAM;YAAW,MAAM;QAAY;QACrC;YAAE,MAAM;YAAO,MAAM;QAAO;KAC7B;IAED,qBACE,6LAAC,uMAAM,CAAC,MAAM;QACZ,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;QAC7C,WAAW,IAAA,4HAAE,EACX,+DACA,aACI,oDACA;kBAGN,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,uMAAM,CAAC,GAAG;4BACT,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,0MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,6LAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;sCAIpD,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,6LAAC,uMAAM,CAAC,CAAC;oCAEP,MAAM,KAAK,IAAI;oCACf,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO,QAAQ,MAAM;oCAAI;oCACvC,WAAU;;wCAET,KAAK,IAAI;sDACV,6LAAC;4CAAK,WAAU;;;;;;;mCARX,KAAK,IAAI;;;;;;;;;;sCAcpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+IAAM;oCAAC,SAAQ;oCAAQ,WAAU;8CAAiC;;;;;;8CAGnE,6LAAC,+IAAM;oCAAC,SAAQ;oCAAW,WAAU;8CAA4B;;;;;;;;;;;;sCAMnE,6LAAC;4BACC,SAAS,IAAM,oBAAoB,CAAC;4BACpC,WAAU;sCAET,iCAAmB,6LAAC,oMAAC;gCAAC,MAAM;;;;;yFAAS,6LAAC,6MAAI;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAKtD,6LAAC,uMAAM,CAAC,GAAG;oBACT,SAAS;oBACT,SAAS;wBACP,QAAQ,mBAAmB,SAAS;wBACpC,SAAS,mBAAmB,IAAI;oBAClC;oBACA,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC;oCAEC,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,oBAAoB;8CAElC,KAAK,IAAI;mCALL,KAAK,IAAI;;;;;0CAQlB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+IAAM;wCAAC,SAAQ;wCAAQ,WAAU;kDAAwC;;;;;;kDAG1E,6LAAC,+IAAM;wCAAC,SAAQ;wCAAW,WAAU;kDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5D;GArHM;KAAA;uCAuHS", "debugId": null}}, {"offset": {"line": 392, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/src/components/Hero.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { ArrowR<PERSON>, Play, Star, Users, Zap, TrendingUp } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport AnimatedCounter from '@/components/AnimatedCounter'\n\nconst Hero = () => {\n  const stats = [\n    { icon: Users, value: 2000, suffix: '+', label: 'Активных магазинов' },\n    { icon: TrendingUp, value: 150, suffix: '%', label: 'Рост продаж' },\n    { icon: Star, value: 4.9, suffix: '', label: 'Рейтинг клиентов' },\n  ]\n\n  const floatingElements = [\n    { icon: '💰', delay: 0, x: 100, y: 50 },\n    { icon: '🚀', delay: 1, x: -80, y: 80 },\n    { icon: '⚡', delay: 2, x: 120, y: -60 },\n    { icon: '📱', delay: 0.5, x: -100, y: -40 },\n    { icon: '🎯', delay: 1.5, x: 80, y: 100 },\n  ]\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background Effects */}\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/20 rounded-full blur-3xl animate-pulse\"></div>\n        <div className=\"absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/20 rounded-full blur-3xl animate-pulse delay-1000\"></div>\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-conic from-blue-500/10 via-purple-500/10 to-blue-500/10 rounded-full blur-3xl animate-spin\" style={{ animationDuration: '20s' }}></div>\n      </div>\n\n      {/* Floating Elements */}\n      {floatingElements.map((element, index) => (\n        <motion.div\n          key={index}\n          initial={{ opacity: 0, scale: 0 }}\n          animate={{ \n            opacity: 1, \n            scale: 1,\n            x: [0, element.x * 0.5, element.x, element.x * 0.5, 0],\n            y: [0, element.y * 0.5, element.y, element.y * 0.5, 0],\n          }}\n          transition={{\n            delay: element.delay,\n            duration: 8,\n            repeat: Infinity,\n            ease: 'easeInOut',\n          }}\n          className=\"absolute text-4xl opacity-20 pointer-events-none\"\n          style={{\n            left: `${50 + (element.x / 10)}%`,\n            top: `${50 + (element.y / 10)}%`,\n          }}\n        >\n          {element.icon}\n        </motion.div>\n      ))}\n\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        <div className=\"text-center max-w-5xl mx-auto\">\n          {/* Badge */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"inline-flex items-center space-x-2 bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-sm border border-white/10 rounded-full px-6 py-2 mb-8\"\n          >\n            <Zap className=\"w-4 h-4 text-yellow-400\" />\n            <span className=\"text-sm text-gray-300\">Новое поколение Telegram магазинов</span>\n          </motion.div>\n\n          {/* Main Heading */}\n          <motion.h1\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            className=\"text-4xl sm:text-5xl lg:text-7xl font-bold mb-6 leading-tight\"\n          >\n            Создайте{' '}\n            <span className=\"gradient-text\">магазин мечты</span>\n            <br />\n            в Telegram за{' '}\n            <motion.span\n              animate={{ \n                backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],\n              }}\n              transition={{ duration: 3, repeat: Infinity }}\n              className=\"bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent bg-[length:200%_100%]\"\n            >\n              15 минут\n            </motion.span>\n          </motion.h1>\n\n          {/* Subtitle */}\n          <motion.p\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            className=\"text-xl sm:text-2xl text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed\"\n          >\n            Современная платформа для создания профессиональных интернет-магазинов в Telegram. \n            Больше продаж, меньше затрат, максимум автоматизации.\n          </motion.p>\n\n          {/* CTA Buttons */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.6 }}\n            className=\"flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6 mb-16\"\n          >\n            <Button \n              size=\"lg\" \n              variant=\"gradient\" \n              className=\"text-lg px-8 py-4 shadow-2xl hover:shadow-blue-500/25 transition-all duration-300 group\"\n            >\n              Создать магазин бесплатно\n              <ArrowRight className=\"ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform\" />\n            </Button>\n            \n            <Button \n              size=\"lg\" \n              variant=\"ghost\" \n              className=\"text-lg px-8 py-4 border border-white/20 hover:border-white/40 group\"\n            >\n              <Play className=\"mr-2 w-5 h-5 group-hover:scale-110 transition-transform\" />\n              Смотреть демо\n            </Button>\n          </motion.div>\n\n          {/* Stats */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.8 }}\n            className=\"grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-2xl mx-auto\"\n          >\n            {stats.map((stat, index) => (\n              <motion.div\n                key={index}\n                whileHover={{ scale: 1.05 }}\n                className=\"glass rounded-2xl p-6 text-center group hover:bg-white/10 transition-all duration-300\"\n              >\n                <stat.icon className=\"w-8 h-8 mx-auto mb-3 text-blue-400 group-hover:text-blue-300 transition-colors\" />\n                <div className=\"text-3xl font-bold text-white mb-1\">{stat.value}</div>\n                <div className=\"text-gray-400 text-sm\">{stat.label}</div>\n              </motion.div>\n            ))}\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ delay: 1.5 }}\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n      >\n        <motion.div\n          animate={{ y: [0, 10, 0] }}\n          transition={{ duration: 2, repeat: Infinity }}\n          className=\"w-6 h-10 border-2 border-white/30 rounded-full flex justify-center\"\n        >\n          <motion.div\n            animate={{ y: [0, 12, 0] }}\n            transition={{ duration: 2, repeat: Infinity }}\n            className=\"w-1 h-3 bg-white/50 rounded-full mt-2\"\n          />\n        </motion.div>\n      </motion.div>\n    </section>\n  )\n}\n\nexport default Hero\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAOA,MAAM,OAAO;IACX,MAAM,QAAQ;QACZ;YAAE,MAAM,gNAAK;YAAE,OAAO;YAAM,QAAQ;YAAK,OAAO;QAAqB;QACrE;YAAE,MAAM,mOAAU;YAAE,OAAO;YAAK,QAAQ;YAAK,OAAO;QAAc;QAClE;YAAE,MAAM,6MAAI;YAAE,OAAO;YAAK,QAAQ;YAAI,OAAO;QAAmB;KACjE;IAED,MAAM,mBAAmB;QACvB;YAAE,MAAM;YAAM,OAAO;YAAG,GAAG;YAAK,GAAG;QAAG;QACtC;YAAE,MAAM;YAAM,OAAO;YAAG,GAAG,CAAC;YAAI,GAAG;QAAG;QACtC;YAAE,MAAM;YAAK,OAAO;YAAG,GAAG;YAAK,GAAG,CAAC;QAAG;QACtC;YAAE,MAAM;YAAM,OAAO;YAAK,GAAG,CAAC;YAAK,GAAG,CAAC;QAAG;QAC1C;YAAE,MAAM;YAAM,OAAO;YAAK,GAAG;YAAI,GAAG;QAAI;KACzC;IAED,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;wBAAmM,OAAO;4BAAE,mBAAmB;wBAAM;;;;;;;;;;;;YAIrP,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,6LAAC,uMAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAE;oBAChC,SAAS;wBACP,SAAS;wBACT,OAAO;wBACP,GAAG;4BAAC;4BAAG,QAAQ,CAAC,GAAG;4BAAK,QAAQ,CAAC;4BAAE,QAAQ,CAAC,GAAG;4BAAK;yBAAE;wBACtD,GAAG;4BAAC;4BAAG,QAAQ,CAAC,GAAG;4BAAK,QAAQ,CAAC;4BAAE,QAAQ,CAAC,GAAG;4BAAK;yBAAE;oBACxD;oBACA,YAAY;wBACV,OAAO,QAAQ,KAAK;wBACpB,UAAU;wBACV,QAAQ;wBACR,MAAM;oBACR;oBACA,WAAU;oBACV,OAAO;wBACL,MAAM,AAAC,GAAwB,OAAtB,KAAM,QAAQ,CAAC,GAAG,IAAI;wBAC/B,KAAK,AAAC,GAAwB,OAAtB,KAAM,QAAQ,CAAC,GAAG,IAAI;oBAChC;8BAEC,QAAQ,IAAI;mBApBR;;;;;0BAwBT,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,uMAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;8CAEV,6LAAC,0MAAG;oCAAC,WAAU;;;;;;8CACf,6LAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;sCAI1C,6LAAC,uMAAM,CAAC,EAAE;4BACR,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;gCACX;gCACU;8CACT,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,6LAAC;;;;;gCAAK;gCACQ;8CACd,6LAAC,uMAAM,CAAC,IAAI;oCACV,SAAS;wCACP,oBAAoB;4CAAC;4CAAU;4CAAY;yCAAS;oCACtD;oCACA,YAAY;wCAAE,UAAU;wCAAG,QAAQ;oCAAS;oCAC5C,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC,uMAAM,CAAC,CAAC;4BACP,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCACX;;;;;;sCAMD,6LAAC,uMAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC,+IAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,WAAU;;wCACX;sDAEC,6LAAC,mOAAU;4CAAC,WAAU;;;;;;;;;;;;8CAGxB,6LAAC,+IAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,WAAU;;sDAEV,6LAAC,6MAAI;4CAAC,WAAU;;;;;;wCAA4D;;;;;;;;;;;;;sCAMhF,6LAAC,uMAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCAET,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,uMAAM,CAAC,GAAG;oCAET,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,WAAU;;sDAEV,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;sDACrB,6LAAC;4CAAI,WAAU;sDAAsC,KAAK,KAAK;;;;;;sDAC/D,6LAAC;4CAAI,WAAU;sDAAyB,KAAK,KAAK;;;;;;;mCAN7C;;;;;;;;;;;;;;;;;;;;;0BAcf,6LAAC,uMAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;0BAEV,cAAA,6LAAC,uMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG;4BAAC;4BAAG;4BAAI;yBAAE;oBAAC;oBACzB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;oBAAS;oBAC5C,WAAU;8BAEV,cAAA,6LAAC,uMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,GAAG;gCAAC;gCAAG;gCAAI;6BAAE;wBAAC;wBACzB,YAAY;4BAAE,UAAU;4BAAG,QAAQ;wBAAS;wBAC5C,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMtB;KAtKM;uCAwKS", "debugId": null}}, {"offset": {"line": 850, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/src/components/Features.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { \n  Smartphone, \n  Zap, \n  Shield, \n  TrendingUp, \n  Users, \n  Settings,\n  CreditCard,\n  BarChart3,\n  Palette,\n  Globe\n} from 'lucide-react'\n\nconst Features = () => {\n  const features = [\n    {\n      icon: Smartphone,\n      title: 'Нативный Telegram опыт',\n      description: 'Магазин интегрируется прямо в Telegram без переходов на внешние сайты',\n      color: 'from-blue-400 to-blue-600',\n      delay: 0\n    },\n    {\n      icon: Zap,\n      title: 'Запуск за 15 минут',\n      description: 'Простая настройка без технических знаний. Загрузите товары и начинайте продавать',\n      color: 'from-yellow-400 to-orange-500',\n      delay: 0.1\n    },\n    {\n      icon: TrendingUp,\n      title: 'Высокая конверсия',\n      description: 'До 21% конверсии против 2% на обычных сайтах благодаря удобству Telegram',\n      color: 'from-green-400 to-green-600',\n      delay: 0.2\n    },\n    {\n      icon: CreditCard,\n      title: 'Множество способов оплаты',\n      description: 'TON, Telegram Payments, банковские карты и криптовалюты',\n      color: 'from-purple-400 to-purple-600',\n      delay: 0.3\n    },\n    {\n      icon: Palette,\n      title: 'Кастомный дизайн',\n      description: 'Полная настройка внешнего вида под ваш бренд с анимациями и эффектами',\n      color: 'from-pink-400 to-rose-500',\n      delay: 0.4\n    },\n    {\n      icon: BarChart3,\n      title: 'Аналитика и отчёты',\n      description: 'Детальная статистика продаж, поведения клиентов и эффективности',\n      color: 'from-indigo-400 to-indigo-600',\n      delay: 0.5\n    },\n    {\n      icon: Users,\n      title: 'CRM система',\n      description: 'Управление клиентами, заказами и автоматические уведомления',\n      color: 'from-teal-400 to-teal-600',\n      delay: 0.6\n    },\n    {\n      icon: Shield,\n      title: 'Безопасность',\n      description: 'Защищённые платежи и данные клиентов с шифрованием',\n      color: 'from-red-400 to-red-600',\n      delay: 0.7\n    },\n    {\n      icon: Settings,\n      title: 'Автоматизация',\n      description: 'Автоматическое обновление остатков, цен и обработка заказов',\n      color: 'from-gray-400 to-gray-600',\n      delay: 0.8\n    },\n    {\n      icon: Globe,\n      title: 'Мультиязычность',\n      description: 'Поддержка множества языков для международных продаж',\n      color: 'from-cyan-400 to-cyan-600',\n      delay: 0.9\n    }\n  ]\n\n  return (\n    <section id=\"features\" className=\"py-20 lg:py-32 relative overflow-hidden\">\n      {/* Background Effects */}\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute top-1/4 right-0 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl\"></div>\n        <div className=\"absolute bottom-1/4 left-0 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-20\"\n        >\n          <h2 className=\"text-4xl lg:text-6xl font-bold mb-6\">\n            Почему выбирают{' '}\n            <span className=\"gradient-text\">нас</span>\n          </h2>\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n            Современные технологии и проверенные решения для максимального роста вашего бизнеса\n          </p>\n        </motion.div>\n\n        {/* Features Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6\">\n          {features.map((feature, index) => (\n            <motion.div\n              key={index}\n              initial={{ opacity: 0, y: 50 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: feature.delay }}\n              viewport={{ once: true }}\n              whileHover={{ \n                y: -10, \n                scale: 1.05,\n                transition: { duration: 0.2 }\n              }}\n              className={`glass rounded-2xl p-6 group hover:bg-white/10 transition-all duration-300 ${\n                index < 5 ? 'lg:col-span-1' : 'md:col-span-1'\n              }`}\n            >\n              {/* Icon */}\n              <motion.div\n                whileHover={{ rotate: 360 }}\n                transition={{ duration: 0.6 }}\n                className={`inline-flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-r ${feature.color} mb-4 group-hover:shadow-lg transition-all duration-300`}\n              >\n                <feature.icon className=\"w-6 h-6 text-white\" />\n              </motion.div>\n\n              {/* Content */}\n              <h3 className=\"text-lg font-semibold text-white mb-3 group-hover:text-blue-300 transition-colors\">\n                {feature.title}\n              </h3>\n              <p className=\"text-gray-400 text-sm leading-relaxed group-hover:text-gray-300 transition-colors\">\n                {feature.description}\n              </p>\n\n              {/* Hover Effect */}\n              <div className={`absolute inset-0 rounded-2xl bg-gradient-to-r ${feature.color} opacity-0 group-hover:opacity-5 transition-opacity duration-300`}></div>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Bottom Stats */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"mt-20 grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto\"\n        >\n          {[\n            { value: '2000+', label: 'Активных магазинов' },\n            { value: '150%', label: 'Средний рост продаж' },\n            { value: '15 мин', label: 'Время запуска' },\n            { value: '24/7', label: 'Поддержка клиентов' }\n          ].map((stat, index) => (\n            <motion.div\n              key={index}\n              whileHover={{ scale: 1.05 }}\n              className=\"text-center group\"\n            >\n              <div className=\"text-3xl lg:text-4xl font-bold gradient-text mb-2 group-hover:scale-110 transition-transform\">\n                {stat.value}\n              </div>\n              <div className=\"text-gray-400 text-sm\">{stat.label}</div>\n            </motion.div>\n          ))}\n        </motion.div>\n      </div>\n    </section>\n  )\n}\n\nexport default Features\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAgBA,MAAM,WAAW;IACf,MAAM,WAAW;QACf;YACE,MAAM,+NAAU;YAChB,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM,0MAAG;YACT,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM,mOAAU;YAChB,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM,mOAAU;YAChB,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM,sNAAO;YACb,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM,kOAAS;YACf,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM,gNAAK;YACX,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM,mNAAM;YACZ,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM,yNAAQ;YACd,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM,gNAAK;YACX,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;QACT;KACD;IAED,qBACE,6LAAC;QAAQ,IAAG;QAAW,WAAU;;0BAE/B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,uMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;;oCAAsC;oCAClC;kDAChB,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,6LAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAMzD,6LAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,uMAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ,KAAK;gCAAC;gCAClD,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCACV,GAAG,CAAC;oCACJ,OAAO;oCACP,YAAY;wCAAE,UAAU;oCAAI;gCAC9B;gCACA,WAAW,AAAC,6EAEX,OADC,QAAQ,IAAI,kBAAkB;;kDAIhC,6LAAC,uMAAM,CAAC,GAAG;wCACT,YAAY;4CAAE,QAAQ;wCAAI;wCAC1B,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAW,AAAC,iFAA8F,OAAd,QAAQ,KAAK,EAAC;kDAE1G,cAAA,6LAAC,QAAQ,IAAI;4CAAC,WAAU;;;;;;;;;;;kDAI1B,6LAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK;;;;;;kDAEhB,6LAAC;wCAAE,WAAU;kDACV,QAAQ,WAAW;;;;;;kDAItB,6LAAC;wCAAI,WAAW,AAAC,iDAA8D,OAAd,QAAQ,KAAK,EAAC;;;;;;;+BAhC1E;;;;;;;;;;kCAsCX,6LAAC,uMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAET;4BACC;gCAAE,OAAO;gCAAS,OAAO;4BAAqB;4BAC9C;gCAAE,OAAO;gCAAQ,OAAO;4BAAsB;4BAC9C;gCAAE,OAAO;gCAAU,OAAO;4BAAgB;4BAC1C;gCAAE,OAAO;gCAAQ,OAAO;4BAAqB;yBAC9C,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC,uMAAM,CAAC,GAAG;gCAET,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;kDACZ,KAAK,KAAK;;;;;;kDAEb,6LAAC;wCAAI,WAAU;kDAAyB,KAAK,KAAK;;;;;;;+BAP7C;;;;;;;;;;;;;;;;;;;;;;AAcnB;KA1KM;uCA4KS", "debugId": null}}, {"offset": {"line": 1195, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/src/components/ParallaxSection.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useRef } from 'react'\nimport { motion, useScroll, useTransform } from 'framer-motion'\nimport { Rocket, Target, Zap, Crown } from 'lucide-react'\n\nconst ParallaxSection = () => {\n  const containerRef = useRef<HTMLDivElement>(null)\n  const { scrollYProgress } = useScroll({\n    target: containerRef,\n    offset: [\"start end\", \"end start\"]\n  })\n\n  const y1 = useTransform(scrollYProgress, [0, 1], [0, -100])\n  const y2 = useTransform(scrollYProgress, [0, 1], [0, 100])\n  const y3 = useTransform(scrollYProgress, [0, 1], [0, -50])\n  const opacity = useTransform(scrollYProgress, [0, 0.3, 0.7, 1], [0, 1, 1, 0])\n\n  const achievements = [\n    {\n      icon: Rocket,\n      title: 'Быстрый запуск',\n      description: 'Создайте магазин за 15 минут',\n      color: 'from-orange-400 to-red-500'\n    },\n    {\n      icon: Target,\n      title: 'Высокая конверсия',\n      description: 'До 21% против 2% на сайтах',\n      color: 'from-green-400 to-emerald-500'\n    },\n    {\n      icon: Zap,\n      title: 'Мгновенные платежи',\n      description: 'Оплата прямо в Telegram',\n      color: 'from-yellow-400 to-orange-500'\n    },\n    {\n      icon: Crown,\n      title: 'Премиум поддержка',\n      description: '24/7 помощь экспертов',\n      color: 'from-purple-400 to-pink-500'\n    }\n  ]\n\n  return (\n    <section ref={containerRef} className=\"py-32 relative overflow-hidden\">\n      {/* Animated Background */}\n      <div className=\"absolute inset-0\">\n        <motion.div\n          style={{ y: y1 }}\n          className=\"absolute top-1/4 left-1/4 w-64 h-64 bg-blue-500/20 rounded-full blur-3xl\"\n        />\n        <motion.div\n          style={{ y: y2 }}\n          className=\"absolute bottom-1/4 right-1/4 w-64 h-64 bg-purple-500/20 rounded-full blur-3xl\"\n        />\n        <motion.div\n          style={{ y: y3 }}\n          className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-conic from-blue-500/10 via-purple-500/10 to-blue-500/10 rounded-full blur-3xl\"\n        />\n      </div>\n\n      <motion.div\n        style={{ opacity }}\n        className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\"\n      >\n        <div className=\"text-center mb-16\">\n          <motion.h2\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"text-4xl lg:text-6xl font-bold mb-6\"\n          >\n            Почему{' '}\n            <span className=\"gradient-text\">TeleShop Pro</span>\n            <br />\n            лучший выбор?\n          </motion.h2>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {achievements.map((achievement, index) => (\n            <motion.div\n              key={index}\n              initial={{ opacity: 0, y: 50 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: index * 0.2 }}\n              viewport={{ once: true }}\n              whileHover={{ \n                y: -20, \n                scale: 1.05,\n                transition: { duration: 0.3 }\n              }}\n              className=\"relative group\"\n            >\n              {/* Glow Effect */}\n              <div className={`absolute inset-0 bg-gradient-to-r ${achievement.color} opacity-0 group-hover:opacity-20 blur-xl transition-opacity duration-500 rounded-3xl`}></div>\n              \n              {/* Card */}\n              <div className=\"relative glass rounded-3xl p-8 text-center border border-white/10 group-hover:border-white/30 transition-all duration-300\">\n                <motion.div\n                  whileHover={{ rotate: 360 }}\n                  transition={{ duration: 0.6 }}\n                  className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-r ${achievement.color} mb-6 group-hover:shadow-2xl transition-all duration-300`}\n                >\n                  <achievement.icon className=\"w-8 h-8 text-white\" />\n                </motion.div>\n                \n                <h3 className=\"text-xl font-bold text-white mb-3 group-hover:text-blue-300 transition-colors\">\n                  {achievement.title}\n                </h3>\n                \n                <p className=\"text-gray-400 group-hover:text-gray-300 transition-colors\">\n                  {achievement.description}\n                </p>\n\n                {/* Floating particles */}\n                <div className=\"absolute inset-0 pointer-events-none\">\n                  {[...Array(3)].map((_, i) => (\n                    <motion.div\n                      key={i}\n                      className=\"absolute w-1 h-1 bg-white/30 rounded-full\"\n                      style={{\n                        left: `${20 + i * 30}%`,\n                        top: `${30 + i * 20}%`,\n                      }}\n                      animate={{\n                        y: [-10, 10, -10],\n                        opacity: [0.3, 0.8, 0.3],\n                      }}\n                      transition={{\n                        duration: 2 + i * 0.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\",\n                      }}\n                    />\n                  ))}\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Bottom Quote */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-20\"\n        >\n          <div className=\"glass rounded-3xl p-8 max-w-4xl mx-auto\">\n            <motion.div\n              animate={{ \n                scale: [1, 1.02, 1],\n              }}\n              transition={{ \n                duration: 4, \n                repeat: Infinity,\n                ease: \"easeInOut\"\n              }}\n              className=\"text-2xl lg:text-3xl font-bold text-white mb-4\"\n            >\n              \"TeleShop Pro изменил наш бизнес. Продажи выросли на 300% за первый месяц!\"\n            </motion.div>\n            <div className=\"flex items-center justify-center space-x-4\">\n              <div className=\"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center\">\n                <span className=\"text-white font-bold\">АК</span>\n              </div>\n              <div className=\"text-left\">\n                <div className=\"text-white font-semibold\">Алексей Козлов</div>\n                <div className=\"text-gray-400 text-sm\">Основатель Fashion Store</div>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n      </motion.div>\n    </section>\n  )\n}\n\nexport default ParallaxSection\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;;;AAJA;;;;AAMA,MAAM,kBAAkB;;IACtB,MAAM,eAAe,IAAA,uKAAM,EAAiB;IAC5C,MAAM,EAAE,eAAe,EAAE,GAAG,IAAA,yLAAS,EAAC;QACpC,QAAQ;QACR,QAAQ;YAAC;YAAa;SAAY;IACpC;IAEA,MAAM,KAAK,IAAA,+LAAY,EAAC,iBAAiB;QAAC;QAAG;KAAE,EAAE;QAAC;QAAG,CAAC;KAAI;IAC1D,MAAM,KAAK,IAAA,+LAAY,EAAC,iBAAiB;QAAC;QAAG;KAAE,EAAE;QAAC;QAAG;KAAI;IACzD,MAAM,KAAK,IAAA,+LAAY,EAAC,iBAAiB;QAAC;QAAG;KAAE,EAAE;QAAC;QAAG,CAAC;KAAG;IACzD,MAAM,UAAU,IAAA,+LAAY,EAAC,iBAAiB;QAAC;QAAG;QAAK;QAAK;KAAE,EAAE;QAAC;QAAG;QAAG;QAAG;KAAE;IAE5E,MAAM,eAAe;QACnB;YACE,MAAM,mNAAM;YACZ,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,mNAAM;YACZ,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,0MAAG;YACT,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,gNAAK;YACX,OAAO;YACP,aAAa;YACb,OAAO;QACT;KACD;IAED,qBACE,6LAAC;QAAQ,KAAK;QAAc,WAAU;;0BAEpC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uMAAM,CAAC,GAAG;wBACT,OAAO;4BAAE,GAAG;wBAAG;wBACf,WAAU;;;;;;kCAEZ,6LAAC,uMAAM,CAAC,GAAG;wBACT,OAAO;4BAAE,GAAG;wBAAG;wBACf,WAAU;;;;;;kCAEZ,6LAAC,uMAAM,CAAC,GAAG;wBACT,OAAO;4BAAE,GAAG;wBAAG;wBACf,WAAU;;;;;;;;;;;;0BAId,6LAAC,uMAAM,CAAC,GAAG;gBACT,OAAO;oBAAE;gBAAQ;gBACjB,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,uMAAM,CAAC,EAAE;4BACR,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;gCACX;gCACQ;8CACP,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,6LAAC;;;;;gCAAK;;;;;;;;;;;;kCAKV,6LAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,6LAAC,uMAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCACV,GAAG,CAAC;oCACJ,OAAO;oCACP,YAAY;wCAAE,UAAU;oCAAI;gCAC9B;gCACA,WAAU;;kDAGV,6LAAC;wCAAI,WAAW,AAAC,qCAAsD,OAAlB,YAAY,KAAK,EAAC;;;;;;kDAGvE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAM,CAAC,GAAG;gDACT,YAAY;oDAAE,QAAQ;gDAAI;gDAC1B,YAAY;oDAAE,UAAU;gDAAI;gDAC5B,WAAW,AAAC,kFAAmG,OAAlB,YAAY,KAAK,EAAC;0DAE/G,cAAA,6LAAC,YAAY,IAAI;oDAAC,WAAU;;;;;;;;;;;0DAG9B,6LAAC;gDAAG,WAAU;0DACX,YAAY,KAAK;;;;;;0DAGpB,6LAAC;gDAAE,WAAU;0DACV,YAAY,WAAW;;;;;;0DAI1B,6LAAC;gDAAI,WAAU;0DACZ;uDAAI,MAAM;iDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,uMAAM,CAAC,GAAG;wDAET,WAAU;wDACV,OAAO;4DACL,MAAM,AAAC,GAAc,OAAZ,KAAK,IAAI,IAAG;4DACrB,KAAK,AAAC,GAAc,OAAZ,KAAK,IAAI,IAAG;wDACtB;wDACA,SAAS;4DACP,GAAG;gEAAC,CAAC;gEAAI;gEAAI,CAAC;6DAAG;4DACjB,SAAS;gEAAC;gEAAK;gEAAK;6DAAI;wDAC1B;wDACA,YAAY;4DACV,UAAU,IAAI,IAAI;4DAClB,QAAQ;4DACR,MAAM;wDACR;uDAdK;;;;;;;;;;;;;;;;;+BArCR;;;;;;;;;;kCA6DX,6LAAC,uMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uMAAM,CAAC,GAAG;oCACT,SAAS;wCACP,OAAO;4CAAC;4CAAG;4CAAM;yCAAE;oCACrB;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,MAAM;oCACR;oCACA,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAuB;;;;;;;;;;;sDAEzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAA2B;;;;;;8DAC1C,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvD;GA/KM;;QAEwB,yLAAS;QAK1B,+LAAY;QACZ,+LAAY;QACZ,+LAAY;QACP,+LAAY;;;KAVxB;uCAiLS", "debugId": null}}, {"offset": {"line": 1637, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/src/components/Examples.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { ExternalLink, Star, TrendingUp, Users, ShoppingBag } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\n\nconst Examples = () => {\n  const examples = [\n    {\n      name: 'Fashion Store',\n      category: 'Одежда и аксессуары',\n      description: 'Стильный магазин модной одежды с интуитивным каталогом и быстрой оплатой',\n      image: '/api/placeholder/400/300',\n      stats: {\n        sales: '+250%',\n        customers: '15K+',\n        rating: '4.9'\n      },\n      features: ['Кастомный дизайн', 'Интеграция с доставкой', 'Автоматические уведомления'],\n      color: 'from-pink-500 to-rose-500'\n    },\n    {\n      name: 'Tech Gadgets',\n      category: 'Электроника',\n      description: 'Магазин гаджетов и электроники с детальными описаниями товаров',\n      image: '/api/placeholder/400/300',\n      stats: {\n        sales: '+180%',\n        customers: '8K+',\n        rating: '4.8'\n      },\n      features: ['Сравнение товаров', 'Техническая поддержка', 'Гарантийное обслуживание'],\n      color: 'from-blue-500 to-cyan-500'\n    },\n    {\n      name: 'Healthy Food',\n      category: 'Здоровое питание',\n      description: 'Магазин органических продуктов с системой подписок и доставкой',\n      image: '/api/placeholder/400/300',\n      stats: {\n        sales: '+320%',\n        customers: '22K+',\n        rating: '4.9'\n      },\n      features: ['Подписки на товары', 'Календарь доставки', 'Программа лояльности'],\n      color: 'from-green-500 to-emerald-500'\n    }\n  ]\n\n  return (\n    <section id=\"examples\" className=\"py-20 lg:py-32 relative overflow-hidden\">\n      {/* Background Effects */}\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute top-0 right-1/3 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl\"></div>\n        <div className=\"absolute bottom-0 left-1/3 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl lg:text-6xl font-bold mb-6\">\n            Успешные{' '}\n            <span className=\"gradient-text\">примеры</span>\n          </h2>\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n            Посмотрите, как наши клиенты создают успешные магазины в Telegram и увеличивают продажи\n          </p>\n        </motion.div>\n\n        {/* Examples Grid */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16\">\n          {examples.map((example, index) => (\n            <motion.div\n              key={index}\n              initial={{ opacity: 0, y: 50 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: index * 0.2 }}\n              viewport={{ once: true }}\n              whileHover={{ y: -10, scale: 1.02 }}\n              className=\"glass rounded-3xl overflow-hidden border border-white/10 group\"\n            >\n              {/* Image Placeholder */}\n              <div className={`h-48 bg-gradient-to-br ${example.color} relative overflow-hidden`}>\n                <div className=\"absolute inset-0 bg-black/20\"></div>\n                <div className=\"absolute top-4 right-4\">\n                  <div className=\"glass rounded-full p-2\">\n                    <ExternalLink className=\"w-4 h-4 text-white\" />\n                  </div>\n                </div>\n                <div className=\"absolute bottom-4 left-4\">\n                  <span className=\"glass px-3 py-1 rounded-full text-sm text-white\">\n                    {example.category}\n                  </span>\n                </div>\n              </div>\n\n              {/* Content */}\n              <div className=\"p-6\">\n                <h3 className=\"text-xl font-bold text-white mb-2 group-hover:text-blue-300 transition-colors\">\n                  {example.name}\n                </h3>\n                <p className=\"text-gray-400 text-sm mb-4 leading-relaxed\">\n                  {example.description}\n                </p>\n\n                {/* Stats */}\n                <div className=\"grid grid-cols-3 gap-4 mb-6\">\n                  <div className=\"text-center\">\n                    <div className=\"flex items-center justify-center mb-1\">\n                      <TrendingUp className=\"w-4 h-4 text-green-400 mr-1\" />\n                      <span className=\"text-lg font-bold text-white\">{example.stats.sales}</span>\n                    </div>\n                    <span className=\"text-xs text-gray-400\">Рост продаж</span>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"flex items-center justify-center mb-1\">\n                      <Users className=\"w-4 h-4 text-blue-400 mr-1\" />\n                      <span className=\"text-lg font-bold text-white\">{example.stats.customers}</span>\n                    </div>\n                    <span className=\"text-xs text-gray-400\">Клиентов</span>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"flex items-center justify-center mb-1\">\n                      <Star className=\"w-4 h-4 text-yellow-400 mr-1\" />\n                      <span className=\"text-lg font-bold text-white\">{example.stats.rating}</span>\n                    </div>\n                    <span className=\"text-xs text-gray-400\">Рейтинг</span>\n                  </div>\n                </div>\n\n                {/* Features */}\n                <div className=\"space-y-2 mb-6\">\n                  {example.features.map((feature, featureIndex) => (\n                    <div key={featureIndex} className=\"flex items-center text-sm text-gray-300\">\n                      <div className=\"w-1.5 h-1.5 bg-blue-400 rounded-full mr-2\"></div>\n                      {feature}\n                    </div>\n                  ))}\n                </div>\n\n                {/* CTA */}\n                <Button \n                  variant=\"outline\" \n                  className=\"w-full border-white/20 hover:border-white/40 text-white hover:bg-white/10 group-hover:scale-105 transition-all duration-300\"\n                >\n                  <ShoppingBag className=\"w-4 h-4 mr-2\" />\n                  Посмотреть магазин\n                </Button>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Stats Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"glass rounded-3xl p-8 text-center\"\n        >\n          <h3 className=\"text-2xl font-bold text-white mb-6\">\n            Результаты наших клиентов\n          </h3>\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8\">\n            {[\n              { value: '2000+', label: 'Созданных магазинов', icon: ShoppingBag },\n              { value: '150%', label: 'Средний рост продаж', icon: TrendingUp },\n              { value: '4.9', label: 'Средний рейтинг', icon: Star },\n              { value: '95%', label: 'Довольных клиентов', icon: Users }\n            ].map((stat, index) => (\n              <motion.div\n                key={index}\n                whileHover={{ scale: 1.05 }}\n                className=\"group\"\n              >\n                <div className=\"inline-flex items-center justify-center w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl mb-3 group-hover:shadow-lg transition-all duration-300\">\n                  <stat.icon className=\"w-6 h-6 text-white\" />\n                </div>\n                <div className=\"text-3xl font-bold gradient-text mb-1 group-hover:scale-110 transition-transform\">\n                  {stat.value}\n                </div>\n                <div className=\"text-gray-400 text-sm\">{stat.label}</div>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Bottom CTA */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-16\"\n        >\n          <h3 className=\"text-2xl font-bold text-white mb-4\">\n            Готовы создать свой успешный магазин?\n          </h3>\n          <p className=\"text-gray-300 mb-8 max-w-2xl mx-auto\">\n            Присоединяйтесь к тысячам предпринимателей, которые уже увеличили свои продажи с TeleShop Pro\n          </p>\n          <Button variant=\"gradient\" size=\"lg\" className=\"shadow-2xl hover:shadow-blue-500/25\">\n            Создать магазин бесплатно\n          </Button>\n        </motion.div>\n      </div>\n    </section>\n  )\n}\n\nexport default Examples\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMA,MAAM,WAAW;IACf,MAAM,WAAW;QACf;YACE,MAAM;YACN,UAAU;YACV,aAAa;YACb,OAAO;YACP,OAAO;gBACL,OAAO;gBACP,WAAW;gBACX,QAAQ;YACV;YACA,UAAU;gBAAC;gBAAoB;gBAA0B;aAA6B;YACtF,OAAO;QACT;QACA;YACE,MAAM;YACN,UAAU;YACV,aAAa;YACb,OAAO;YACP,OAAO;gBACL,OAAO;gBACP,WAAW;gBACX,QAAQ;YACV;YACA,UAAU;gBAAC;gBAAqB;gBAAyB;aAA2B;YACpF,OAAO;QACT;QACA;YACE,MAAM;YACN,UAAU;YACV,aAAa;YACb,OAAO;YACP,OAAO;gBACL,OAAO;gBACP,WAAW;gBACX,QAAQ;YACV;YACA,UAAU;gBAAC;gBAAsB;gBAAsB;aAAuB;YAC9E,OAAO;QACT;KACD;IAED,qBACE,6LAAC;QAAQ,IAAG;QAAW,WAAU;;0BAE/B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,uMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;;oCAAsC;oCACzC;kDACT,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,6LAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAMzD,6LAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,uMAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCAAE,GAAG,CAAC;oCAAI,OAAO;gCAAK;gCAClC,WAAU;;kDAGV,6LAAC;wCAAI,WAAW,AAAC,0BAAuC,OAAd,QAAQ,KAAK,EAAC;;0DACtD,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,yOAAY;wDAAC,WAAU;;;;;;;;;;;;;;;;0DAG5B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DACb,QAAQ,QAAQ;;;;;;;;;;;;;;;;;kDAMvB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DACX,QAAQ,IAAI;;;;;;0DAEf,6LAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;0DAItB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,mOAAU;wEAAC,WAAU;;;;;;kFACtB,6LAAC;wEAAK,WAAU;kFAAgC,QAAQ,KAAK,CAAC,KAAK;;;;;;;;;;;;0EAErE,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAE1C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,gNAAK;wEAAC,WAAU;;;;;;kFACjB,6LAAC;wEAAK,WAAU;kFAAgC,QAAQ,KAAK,CAAC,SAAS;;;;;;;;;;;;0EAEzE,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAE1C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,6MAAI;wEAAC,WAAU;;;;;;kFAChB,6LAAC;wEAAK,WAAU;kFAAgC,QAAQ,KAAK,CAAC,MAAM;;;;;;;;;;;;0EAEtE,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAK5C,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC9B,6LAAC;wDAAuB,WAAU;;0EAChC,6LAAC;gEAAI,WAAU;;;;;;4DACd;;uDAFO;;;;;;;;;;0DAQd,6LAAC,+IAAM;gDACL,SAAQ;gDACR,WAAU;;kEAEV,6LAAC,sOAAW;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;+BAxEvC;;;;;;;;;;kCAiFX,6LAAC,uMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;0CAAqC;;;;;;0CAGnD,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,OAAO;wCAAS,OAAO;wCAAuB,MAAM,sOAAW;oCAAC;oCAClE;wCAAE,OAAO;wCAAQ,OAAO;wCAAuB,MAAM,mOAAU;oCAAC;oCAChE;wCAAE,OAAO;wCAAO,OAAO;wCAAmB,MAAM,6MAAI;oCAAC;oCACrD;wCAAE,OAAO;wCAAO,OAAO;wCAAsB,MAAM,gNAAK;oCAAC;iCAC1D,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC,uMAAM,CAAC,GAAG;wCAET,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;;;;;;0DAEvB,6LAAC;gDAAI,WAAU;0DACZ,KAAK,KAAK;;;;;;0DAEb,6LAAC;gDAAI,WAAU;0DAAyB,KAAK,KAAK;;;;;;;uCAV7C;;;;;;;;;;;;;;;;kCAiBb,6LAAC,uMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;0CAAqC;;;;;;0CAGnD,6LAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAGpD,6LAAC,+IAAM;gCAAC,SAAQ;gCAAW,MAAK;gCAAK,WAAU;0CAAsC;;;;;;;;;;;;;;;;;;;;;;;;AAO/F;KAjNM;uCAmNS", "debugId": null}}, {"offset": {"line": 2250, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/src/components/Pricing.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { Check, Star, Zap, Crown } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\n\nconst Pricing = () => {\n  const plans = [\n    {\n      name: 'Start',\n      icon: Zap,\n      color: 'from-green-400 to-green-600',\n      borderColor: 'border-green-500/50',\n      popular: false,\n      price: '149',\n      monthly: '9',\n      description: 'Идеально для начинающих предпринимателей',\n      features: [\n        'Установка Telegram WebApp',\n        'Базовый UI (без кастомного дизайна)',\n        'Подключение оплаты (TON + Telegram Payments)',\n        'Хостинг и база (на твоём сервере)',\n        'Админка через Telegram-бота',\n        'До 20 товаров'\n      ]\n    },\n    {\n      name: 'Pro',\n      icon: Star,\n      color: 'from-blue-400 to-blue-600',\n      borderColor: 'border-blue-500/50',\n      popular: true,\n      price: '249',\n      monthly: '19',\n      description: 'Для серьёзного бизнеса с индивидуальным подходом',\n      features: [\n        'Всё из тарифа Start',\n        'Кастомный дизайн (цвета, шрифты, анимации)',\n        'Логотип + кастомный Telegram theme',\n        'Импорт товаров (до 50) и помощь в описаниях',\n        'Свой домен (если надо)',\n        'До 100 товаров',\n        'Обновления и техподдержка'\n      ]\n    },\n    {\n      name: 'Agency',\n      icon: Crown,\n      color: 'from-purple-400 to-purple-600',\n      borderColor: 'border-purple-500/50',\n      popular: false,\n      price: '499',\n      monthly: '49',\n      description: 'Для агентств и маркетплейсов',\n      features: [\n        'White-label WebApp (без твоего бренда)',\n        'Панель для управления несколькими магазинами',\n        'Отдельный сервер + база данных клиента',\n        'API для внешних интеграций',\n        'Возможность перепродажи клиентам',\n        'Поддержка кастомной разработки (по запросу)',\n        'Приоритетная поддержка 24/7'\n      ]\n    }\n  ]\n\n  return (\n    <section id=\"pricing\" className=\"py-20 lg:py-32 relative overflow-hidden\">\n      {/* Background Effects */}\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute top-0 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl\"></div>\n        <div className=\"absolute bottom-0 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl lg:text-6xl font-bold mb-6\">\n            Выберите свой{' '}\n            <span className=\"gradient-text\">тариф</span>\n          </h2>\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n            Прозрачные цены без скрытых комиссий. Начните бесплатно и масштабируйтесь по мере роста.\n          </p>\n        </motion.div>\n\n        {/* Pricing Cards */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-7xl mx-auto\">\n          {plans.map((plan, index) => (\n            <motion.div\n              key={plan.name}\n              initial={{ opacity: 0, y: 50 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: index * 0.2 }}\n              viewport={{ once: true }}\n              whileHover={{ y: -10, scale: 1.02 }}\n              className={`relative glass rounded-3xl p-8 ${plan.borderColor} border-2 ${\n                plan.popular ? 'lg:scale-105 lg:z-10' : ''\n              } group hover:border-opacity-100 transition-all duration-300`}\n            >\n              {/* Popular Badge */}\n              {plan.popular && (\n                <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2\">\n                  <div className=\"bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-2 rounded-full text-sm font-semibold\">\n                    Популярный выбор\n                  </div>\n                </div>\n              )}\n\n              {/* Plan Header */}\n              <div className=\"text-center mb-8\">\n                <div className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-r ${plan.color} mb-4`}>\n                  <plan.icon className=\"w-8 h-8 text-white\" />\n                </div>\n                <h3 className=\"text-2xl font-bold text-white mb-2\">{plan.name}</h3>\n                <p className=\"text-gray-400 text-sm\">{plan.description}</p>\n              </div>\n\n              {/* Pricing */}\n              <div className=\"text-center mb-8\">\n                <div className=\"flex items-baseline justify-center mb-2\">\n                  <span className=\"text-5xl font-bold text-white\">{plan.price}</span>\n                  <span className=\"text-xl text-gray-400 ml-1\">€</span>\n                </div>\n                <div className=\"text-gray-400 text-sm mb-4\">разовая оплата</div>\n                <div className=\"flex items-baseline justify-center\">\n                  <span className=\"text-2xl font-semibold text-white\">{plan.monthly}</span>\n                  <span className=\"text-gray-400 ml-1\">€/мес</span>\n                </div>\n              </div>\n\n              {/* Features */}\n              <div className=\"space-y-4 mb-8\">\n                {plan.features.map((feature, featureIndex) => (\n                  <motion.div\n                    key={featureIndex}\n                    initial={{ opacity: 0, x: -20 }}\n                    whileInView={{ opacity: 1, x: 0 }}\n                    transition={{ delay: index * 0.2 + featureIndex * 0.1 }}\n                    viewport={{ once: true }}\n                    className=\"flex items-start space-x-3\"\n                  >\n                    <div className={`flex-shrink-0 w-5 h-5 rounded-full bg-gradient-to-r ${plan.color} flex items-center justify-center mt-0.5`}>\n                      <Check className=\"w-3 h-3 text-white\" />\n                    </div>\n                    <span className=\"text-gray-300 text-sm leading-relaxed\">{feature}</span>\n                  </motion.div>\n                ))}\n              </div>\n\n              {/* CTA Button */}\n              <Button\n                variant={plan.popular ? \"gradient\" : \"outline\"}\n                className={`w-full py-3 ${\n                  plan.popular \n                    ? 'shadow-lg hover:shadow-xl' \n                    : 'border-white/20 hover:border-white/40 text-white hover:bg-white/10'\n                } transition-all duration-300 group-hover:scale-105`}\n              >\n                {plan.name === 'Agency' ? 'Связаться с нами' : 'Начать сейчас'}\n              </Button>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Bottom CTA */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-16\"\n        >\n          <p className=\"text-gray-400 mb-6\">\n            Не уверены какой тариф выбрать? Начните с бесплатного периода\n          </p>\n          <Button variant=\"ghost\" className=\"border border-white/20 hover:border-white/40\">\n            Сравнить все тарифы\n          </Button>\n        </motion.div>\n      </div>\n    </section>\n  )\n}\n\nexport default Pricing\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMA,MAAM,UAAU;IACd,MAAM,QAAQ;QACZ;YACE,MAAM;YACN,MAAM,0MAAG;YACT,OAAO;YACP,aAAa;YACb,SAAS;YACT,OAAO;YACP,SAAS;YACT,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QACA;YACE,MAAM;YACN,MAAM,6MAAI;YACV,OAAO;YACP,aAAa;YACb,SAAS;YACT,OAAO;YACP,SAAS;YACT,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QACA;YACE,MAAM;YACN,MAAM,gNAAK;YACX,OAAO;YACP,aAAa;YACb,SAAS;YACT,OAAO;YACP,SAAS;YACT,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;KACD;IAED,qBACE,6LAAC;QAAQ,IAAG;QAAU,WAAU;;0BAE9B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,uMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;;oCAAsC;oCACpC;kDACd,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,6LAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAMzD,6LAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,uMAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCAAE,GAAG,CAAC;oCAAI,OAAO;gCAAK;gCAClC,WAAW,AAAC,kCACV,OAD2C,KAAK,WAAW,EAAC,cAE7D,OADC,KAAK,OAAO,GAAG,yBAAyB,IACzC;;oCAGA,KAAK,OAAO,kBACX,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDAAuG;;;;;;;;;;;kDAO1H,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,AAAC,kFAA4F,OAAX,KAAK,KAAK,EAAC;0DAC3G,cAAA,6LAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;;;;;;0DAEvB,6LAAC;gDAAG,WAAU;0DAAsC,KAAK,IAAI;;;;;;0DAC7D,6LAAC;gDAAE,WAAU;0DAAyB,KAAK,WAAW;;;;;;;;;;;;kDAIxD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAiC,KAAK,KAAK;;;;;;kEAC3D,6LAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;0DAE/C,6LAAC;gDAAI,WAAU;0DAA6B;;;;;;0DAC5C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAqC,KAAK,OAAO;;;;;;kEACjE,6LAAC;wDAAK,WAAU;kEAAqB;;;;;;;;;;;;;;;;;;kDAKzC,6LAAC;wCAAI,WAAU;kDACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC3B,6LAAC,uMAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,YAAY;oDAAE,OAAO,QAAQ,MAAM,eAAe;gDAAI;gDACtD,UAAU;oDAAE,MAAM;gDAAK;gDACvB,WAAU;;kEAEV,6LAAC;wDAAI,WAAW,AAAC,uDAAiE,OAAX,KAAK,KAAK,EAAC;kEAChF,cAAA,6LAAC,gNAAK;4DAAC,WAAU;;;;;;;;;;;kEAEnB,6LAAC;wDAAK,WAAU;kEAAyC;;;;;;;+CAVpD;;;;;;;;;;kDAgBX,6LAAC,+IAAM;wCACL,SAAS,KAAK,OAAO,GAAG,aAAa;wCACrC,WAAW,AAAC,eAIX,OAHC,KAAK,OAAO,GACR,8BACA,sEACL;kDAEA,KAAK,IAAI,KAAK,WAAW,qBAAqB;;;;;;;+BArE5C,KAAK,IAAI;;;;;;;;;;kCA4EpB,6LAAC,uMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,6LAAC,+IAAM;gCAAC,SAAQ;gCAAQ,WAAU;0CAA+C;;;;;;;;;;;;;;;;;;;;;;;;AAO3F;KAvLM;uCAyLS", "debugId": null}}, {"offset": {"line": 2687, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/src/components/FAQ.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { Plus, Minus } from 'lucide-react'\n\nconst FAQ = () => {\n  const [openIndex, setOpenIndex] = useState<number | null>(0)\n\n  const faqs = [\n    {\n      question: 'Что такое TeleShop Pro и чем он отличается от других сервисов?',\n      answer: 'TeleShop Pro — это современная платформа для создания нативных Telegram-магазинов за 15 минут. Мы специализируемся на Telegram Mini App, поэтому интерфейс для ваших клиентов выглядит как покупка на сайте, но быстрее и удобнее. У нас есть весь функционал для e-commerce: интеграции с доставками, управление остатками, аналитика, онлайн-платежи и многое другое.'\n    },\n    {\n      question: 'Могу ли я создать только каталог без онлайн-платежей?',\n      answer: 'Да, конечно! В TeleShop Pro можно создать только каталог и принимать заявки без подключения онлайн-платежей. Создайте кастомный способ оплаты (например, оплата курьеру или наличными), и вы будете получать уведомления о заказах для дальнейшей связи с клиентами.'\n    },\n    {\n      question: 'Сколько стоит использование сервиса?',\n      answer: 'При регистрации вы получаете 14 дней бесплатного пробного периода с полным доступом ко всем функциям. Далее выберите один из тарифов: Start (149€ + 9€/мес), Pro (249€ + 19€/мес) или Agency (от 499€ + от 49€/мес). TeleShop Pro не берет комиссий с продаж и не требует дополнительных платежей.'\n    },\n    {\n      question: 'Что будет после окончания пробного периода?',\n      answer: 'Все данные магазина будут сохранены, но он временно станет недоступен для клиентов, пока вы не оформите подписку. Как только вы выберете тариф, магазин снова заработает со всеми настройками и товарами.'\n    },\n    {\n      question: 'Какие способы оплаты поддерживаются?',\n      answer: 'Мы поддерживаем множество способов оплаты: TON, Telegram Payments, банковские карты, криптовалюты, а также возможность создания кастомных способов оплаты для вашего бизнеса.'\n    },\n    {\n      question: 'Можно ли настроить дизайн под свой бренд?',\n      answer: 'Да! В тарифах Pro и Agency доступна полная кастомизация дизайна: цвета, шрифты, анимации, логотип и даже кастомная Telegram-тема. Мы поможем создать уникальный внешний вид, который отражает ваш бренд.'\n    },\n    {\n      question: 'Есть ли техническая поддержка?',\n      answer: 'Конечно! Мы предоставляем техническую поддержку на всех тарифах. В тарифе Agency доступна приоритетная поддержка 24/7. Наша команда поможет с настройкой, интеграциями и любыми вопросами по использованию платформы.'\n    },\n    {\n      question: 'Можно ли интегрировать магазин с внешними системами?',\n      answer: 'Да! Мы предоставляем API для интеграции с внешними системами, CRM, складскими программами и другими сервисами. В тарифе Agency доступны расширенные возможности интеграции и кастомная разработка.'\n    }\n  ]\n\n  return (\n    <section id=\"faq\" className=\"py-20 lg:py-32 relative overflow-hidden\">\n      {/* Background Effects */}\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute top-1/3 left-1/4 w-96 h-96 bg-green-500/10 rounded-full blur-3xl\"></div>\n        <div className=\"absolute bottom-1/3 right-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl lg:text-6xl font-bold mb-6\">\n            Часто задаваемые{' '}\n            <span className=\"gradient-text\">вопросы</span>\n          </h2>\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n            Ответы на самые популярные вопросы о создании и управлении Telegram-магазинами\n          </p>\n        </motion.div>\n\n        {/* FAQ Items */}\n        <div className=\"max-w-4xl mx-auto\">\n          {faqs.map((faq, index) => (\n            <motion.div\n              key={index}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"mb-4\"\n            >\n              <motion.div\n                whileHover={{ scale: 1.01 }}\n                className=\"glass rounded-2xl border border-white/10 overflow-hidden\"\n              >\n                <button\n                  onClick={() => setOpenIndex(openIndex === index ? null : index)}\n                  className=\"w-full px-6 py-6 text-left flex items-center justify-between hover:bg-white/5 transition-colors duration-200\"\n                >\n                  <h3 className=\"text-lg font-semibold text-white pr-4\">\n                    {faq.question}\n                  </h3>\n                  <motion.div\n                    animate={{ rotate: openIndex === index ? 45 : 0 }}\n                    transition={{ duration: 0.2 }}\n                    className=\"flex-shrink-0\"\n                  >\n                    {openIndex === index ? (\n                      <Minus className=\"w-5 h-5 text-blue-400\" />\n                    ) : (\n                      <Plus className=\"w-5 h-5 text-gray-400\" />\n                    )}\n                  </motion.div>\n                </button>\n                \n                <AnimatePresence>\n                  {openIndex === index && (\n                    <motion.div\n                      initial={{ height: 0, opacity: 0 }}\n                      animate={{ height: 'auto', opacity: 1 }}\n                      exit={{ height: 0, opacity: 0 }}\n                      transition={{ duration: 0.3, ease: 'easeInOut' }}\n                      className=\"overflow-hidden\"\n                    >\n                      <div className=\"px-6 pb-6 pt-0\">\n                        <div className=\"h-px bg-gradient-to-r from-transparent via-white/20 to-transparent mb-4\"></div>\n                        <p className=\"text-gray-300 leading-relaxed\">\n                          {faq.answer}\n                        </p>\n                      </div>\n                    </motion.div>\n                  )}\n                </AnimatePresence>\n              </motion.div>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Bottom CTA */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-16\"\n        >\n          <div className=\"glass rounded-3xl p-8 max-w-2xl mx-auto\">\n            <h3 className=\"text-2xl font-bold text-white mb-4\">\n              Остались вопросы?\n            </h3>\n            <p className=\"text-gray-300 mb-6\">\n              Наша команда поддержки готова помочь вам 24/7. Свяжитесь с нами любым удобным способом.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-300\"\n              >\n                Написать в поддержку\n              </motion.button>\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"px-6 py-3 border border-white/20 text-white rounded-lg font-semibold hover:bg-white/10 transition-all duration-300\"\n              >\n                Запланировать звонок\n              </motion.button>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  )\n}\n\nexport default FAQ\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AACA;AAAA;;;AAJA;;;;AAMA,MAAM,MAAM;;IACV,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAgB;IAE1D,MAAM,OAAO;QACX;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;KACD;IAED,qBACE,6LAAC;QAAQ,IAAG;QAAM,WAAU;;0BAE1B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,uMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;;oCAAsC;oCACjC;kDACjB,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,6LAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAMzD,6LAAC;wBAAI,WAAU;kCACZ,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,6LAAC,uMAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CAEV,cAAA,6LAAC,uMAAM,CAAC,GAAG;oCACT,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,WAAU;;sDAEV,6LAAC;4CACC,SAAS,IAAM,aAAa,cAAc,QAAQ,OAAO;4CACzD,WAAU;;8DAEV,6LAAC;oDAAG,WAAU;8DACX,IAAI,QAAQ;;;;;;8DAEf,6LAAC,uMAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,QAAQ,cAAc,QAAQ,KAAK;oDAAE;oDAChD,YAAY;wDAAE,UAAU;oDAAI;oDAC5B,WAAU;8DAET,cAAc,sBACb,6LAAC,gNAAK;wDAAC,WAAU;;;;;iHAEjB,6LAAC,6MAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAKtB,6LAAC,+MAAe;sDACb,cAAc,uBACb,6LAAC,uMAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,QAAQ;oDAAG,SAAS;gDAAE;gDACjC,SAAS;oDAAE,QAAQ;oDAAQ,SAAS;gDAAE;gDACtC,MAAM;oDAAE,QAAQ;oDAAG,SAAS;gDAAE;gDAC9B,YAAY;oDAAE,UAAU;oDAAK,MAAM;gDAAY;gDAC/C,WAAU;0DAEV,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAE,WAAU;sEACV,IAAI,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BA3ClB;;;;;;;;;;kCAuDX,6LAAC,uMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAqC;;;;;;8CAGnD,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAM,CAAC,MAAM;4CACZ,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;4CACxB,WAAU;sDACX;;;;;;sDAGD,6LAAC,uMAAM,CAAC,MAAM;4CACZ,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;4CACxB,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GA9JM;KAAA;uCAgKS", "debugId": null}}, {"offset": {"line": 3067, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/src/components/Footer.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { Zap, Mail, MessageCircle, Github, Twitter, Linkedin } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\n\nconst Footer = () => {\n  const footerLinks = {\n    product: [\n      { name: 'Возможности', href: '#features' },\n      { name: 'Тарифы', href: '#pricing' },\n      { name: 'Примеры магазинов', href: '#examples' },\n      { name: 'API документация', href: '#' },\n    ],\n    company: [\n      { name: 'О нас', href: '#' },\n      { name: 'Блог', href: '#' },\n      { name: 'Карьера', href: '#' },\n      { name: 'Пресс-кит', href: '#' },\n    ],\n    support: [\n      { name: 'Центр помощи', href: '#' },\n      { name: 'Связаться с нами', href: '#' },\n      { name: 'Статус системы', href: '#' },\n      { name: 'Обновления', href: '#' },\n    ],\n    legal: [\n      { name: 'Политика конфиденциальности', href: '#' },\n      { name: 'Условия использования', href: '#' },\n      { name: 'Соглашение об уровне обслуживания', href: '#' },\n      { name: 'Cookies', href: '#' },\n    ],\n  }\n\n  const socialLinks = [\n    { icon: MessageCircle, href: '#', label: 'Telegram' },\n    { icon: Twitter, href: '#', label: 'Twitter' },\n    { icon: Linkedin, href: '#', label: 'LinkedIn' },\n    { icon: Github, href: '#', label: 'GitHub' },\n  ]\n\n  return (\n    <footer className=\"relative bg-black/20 backdrop-blur-sm border-t border-white/10\">\n      {/* Background Effects */}\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute top-0 left-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl\"></div>\n        <div className=\"absolute bottom-0 right-1/4 w-96 h-96 bg-purple-500/5 rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        {/* Main Footer Content */}\n        <div className=\"py-16\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-12 gap-12\">\n            {/* Brand Section */}\n            <div className=\"lg:col-span-4\">\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6 }}\n                viewport={{ once: true }}\n                className=\"mb-8\"\n              >\n                <div className=\"flex items-center space-x-2 mb-6\">\n                  <div className=\"relative\">\n                    <div className=\"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\">\n                      <Zap className=\"w-6 h-6 text-white\" />\n                    </div>\n                    <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse\"></div>\n                  </div>\n                  <span className=\"text-2xl font-bold gradient-text\">TeleShop Pro</span>\n                </div>\n                <p className=\"text-gray-400 leading-relaxed mb-6\">\n                  Создавайте профессиональные интернет-магазины в Telegram за считанные минуты. \n                  Современные технологии для максимального роста вашего бизнеса.\n                </p>\n                <div className=\"flex space-x-4\">\n                  {socialLinks.map((social, index) => (\n                    <motion.a\n                      key={index}\n                      href={social.href}\n                      whileHover={{ scale: 1.1, y: -2 }}\n                      whileTap={{ scale: 0.95 }}\n                      className=\"w-10 h-10 bg-white/10 hover:bg-white/20 rounded-lg flex items-center justify-center transition-colors group\"\n                      aria-label={social.label}\n                    >\n                      <social.icon className=\"w-5 h-5 text-gray-400 group-hover:text-white transition-colors\" />\n                    </motion.a>\n                  ))}\n                </div>\n              </motion.div>\n            </div>\n\n            {/* Links Sections */}\n            <div className=\"lg:col-span-8\">\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8\">\n                {Object.entries(footerLinks).map(([category, links], categoryIndex) => (\n                  <motion.div\n                    key={category}\n                    initial={{ opacity: 0, y: 20 }}\n                    whileInView={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.6, delay: categoryIndex * 0.1 }}\n                    viewport={{ once: true }}\n                  >\n                    <h3 className=\"text-white font-semibold mb-4 capitalize\">\n                      {category === 'product' && 'Продукт'}\n                      {category === 'company' && 'Компания'}\n                      {category === 'support' && 'Поддержка'}\n                      {category === 'legal' && 'Правовая информация'}\n                    </h3>\n                    <ul className=\"space-y-3\">\n                      {links.map((link, index) => (\n                        <li key={index}>\n                          <a\n                            href={link.href}\n                            className=\"text-gray-400 hover:text-white transition-colors duration-200 text-sm\"\n                          >\n                            {link.name}\n                          </a>\n                        </li>\n                      ))}\n                    </ul>\n                  </motion.div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Newsletter Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"py-8 border-t border-white/10\"\n        >\n          <div className=\"flex flex-col lg:flex-row items-center justify-between space-y-6 lg:space-y-0\">\n            <div className=\"text-center lg:text-left\">\n              <h3 className=\"text-xl font-semibold text-white mb-2\">\n                Будьте в курсе новостей\n              </h3>\n              <p className=\"text-gray-400\">\n                Получайте обновления о новых функциях и советы по развитию бизнеса\n              </p>\n            </div>\n            <div className=\"flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3 w-full lg:w-auto\">\n              <input\n                type=\"email\"\n                placeholder=\"Ваш email\"\n                className=\"px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors flex-1 lg:w-64\"\n              />\n              <Button variant=\"gradient\" className=\"px-6\">\n                <Mail className=\"w-4 h-4 mr-2\" />\n                Подписаться\n              </Button>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Bottom Bar */}\n        <motion.div\n          initial={{ opacity: 0 }}\n          whileInView={{ opacity: 1 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          viewport={{ once: true }}\n          className=\"py-6 border-t border-white/10 flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0\"\n        >\n          <p className=\"text-gray-400 text-sm\">\n            © 2025 TeleShop Pro. Все права защищены.\n          </p>\n          <div className=\"flex items-center space-x-6 text-sm text-gray-400\">\n            <span>Сделано с ❤️ для предпринимателей</span>\n            <div className=\"flex items-center space-x-1\">\n              <div className=\"w-2 h-2 bg-green-400 rounded-full animate-pulse\"></div>\n              <span>Все системы работают</span>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </footer>\n  )\n}\n\nexport default Footer\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMA,MAAM,SAAS;IACb,MAAM,cAAc;QAClB,SAAS;YACP;gBAAE,MAAM;gBAAe,MAAM;YAAY;YACzC;gBAAE,MAAM;gBAAU,MAAM;YAAW;YACnC;gBAAE,MAAM;gBAAqB,MAAM;YAAY;YAC/C;gBAAE,MAAM;gBAAoB,MAAM;YAAI;SACvC;QACD,SAAS;YACP;gBAAE,MAAM;gBAAS,MAAM;YAAI;YAC3B;gBAAE,MAAM;gBAAQ,MAAM;YAAI;YAC1B;gBAAE,MAAM;gBAAW,MAAM;YAAI;YAC7B;gBAAE,MAAM;gBAAa,MAAM;YAAI;SAChC;QACD,SAAS;YACP;gBAAE,MAAM;gBAAgB,MAAM;YAAI;YAClC;gBAAE,MAAM;gBAAoB,MAAM;YAAI;YACtC;gBAAE,MAAM;gBAAkB,MAAM;YAAI;YACpC;gBAAE,MAAM;gBAAc,MAAM;YAAI;SACjC;QACD,OAAO;YACL;gBAAE,MAAM;gBAA+B,MAAM;YAAI;YACjD;gBAAE,MAAM;gBAAyB,MAAM;YAAI;YAC3C;gBAAE,MAAM;gBAAqC,MAAM;YAAI;YACvD;gBAAE,MAAM;gBAAW,MAAM;YAAI;SAC9B;IACH;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM,4OAAa;YAAE,MAAM;YAAK,OAAO;QAAW;QACpD;YAAE,MAAM,sNAAO;YAAE,MAAM;YAAK,OAAO;QAAU;QAC7C;YAAE,MAAM,yNAAQ;YAAE,MAAM;YAAK,OAAO;QAAW;QAC/C;YAAE,MAAM,mNAAM;YAAE,MAAM;YAAK,OAAO;QAAS;KAC5C;IAED,qBACE,6LAAC;QAAO,WAAU;;0BAEhB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uMAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,0MAAG;oEAAC,WAAU;;;;;;;;;;;0EAEjB,6LAAC;gEAAI,WAAU;;;;;;;;;;;;kEAEjB,6LAAC;wDAAK,WAAU;kEAAmC;;;;;;;;;;;;0DAErD,6LAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAIlD,6LAAC;gDAAI,WAAU;0DACZ,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,6LAAC,uMAAM,CAAC,CAAC;wDAEP,MAAM,OAAO,IAAI;wDACjB,YAAY;4DAAE,OAAO;4DAAK,GAAG,CAAC;wDAAE;wDAChC,UAAU;4DAAE,OAAO;wDAAK;wDACxB,WAAU;wDACV,cAAY,OAAO,KAAK;kEAExB,cAAA,6LAAC,OAAO,IAAI;4DAAC,WAAU;;;;;;uDAPlB;;;;;;;;;;;;;;;;;;;;;8CAef,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACZ,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,QAAoB;gDAAnB,CAAC,UAAU,MAAM;iEACjD,6LAAC,uMAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,YAAY;oDAAE,UAAU;oDAAK,OAAO,gBAAgB;gDAAI;gDACxD,UAAU;oDAAE,MAAM;gDAAK;;kEAEvB,6LAAC;wDAAG,WAAU;;4DACX,aAAa,aAAa;4DAC1B,aAAa,aAAa;4DAC1B,aAAa,aAAa;4DAC1B,aAAa,WAAW;;;;;;;kEAE3B,6LAAC;wDAAG,WAAU;kEACX,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;0EACC,cAAA,6LAAC;oEACC,MAAM,KAAK,IAAI;oEACf,WAAU;8EAET,KAAK,IAAI;;;;;;+DALL;;;;;;;;;;;+CAdR;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAgCjB,6LAAC,uMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDAGtD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAI/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;sDAEZ,6LAAC,+IAAM;4CAAC,SAAQ;4CAAW,WAAU;;8DACnC,6LAAC,6MAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;kCAQzC,6LAAC,uMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;wBAAE;wBACtB,aAAa;4BAAE,SAAS;wBAAE;wBAC1B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;0CAGrC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAK;;;;;;kDACN,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB;KA/KM;uCAiLS", "debugId": null}}]}