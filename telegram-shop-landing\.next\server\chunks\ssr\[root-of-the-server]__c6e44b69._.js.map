{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/src/components/FloatingParticles.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useRef } from 'react'\nimport { motion } from 'framer-motion'\n\ninterface Particle {\n  id: number\n  x: number\n  y: number\n  size: number\n  speedX: number\n  speedY: number\n  opacity: number\n}\n\nconst FloatingParticles = () => {\n  const containerRef = useRef<HTMLDivElement>(null)\n  const particlesRef = useRef<Particle[]>([])\n  const animationRef = useRef<number>()\n\n  useEffect(() => {\n    const container = containerRef.current\n    if (!container) return\n\n    // Initialize particles\n    const particleCount = 50\n    particlesRef.current = Array.from({ length: particleCount }, (_, i) => ({\n      id: i,\n      x: Math.random() * window.innerWidth,\n      y: Math.random() * window.innerHeight,\n      size: Math.random() * 3 + 1,\n      speedX: (Math.random() - 0.5) * 0.5,\n      speedY: (Math.random() - 0.5) * 0.5,\n      opacity: Math.random() * 0.5 + 0.1,\n    }))\n\n    const animate = () => {\n      particlesRef.current.forEach(particle => {\n        particle.x += particle.speedX\n        particle.y += particle.speedY\n\n        // Wrap around screen\n        if (particle.x > window.innerWidth) particle.x = 0\n        if (particle.x < 0) particle.x = window.innerWidth\n        if (particle.y > window.innerHeight) particle.y = 0\n        if (particle.y < 0) particle.y = window.innerHeight\n      })\n\n      animationRef.current = requestAnimationFrame(animate)\n    }\n\n    animate()\n\n    return () => {\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current)\n      }\n    }\n  }, [])\n\n  return (\n    <div ref={containerRef} className=\"fixed inset-0 pointer-events-none z-0\">\n      {particlesRef.current.map(particle => (\n        <motion.div\n          key={particle.id}\n          className=\"absolute bg-white/20 rounded-full\"\n          style={{\n            width: particle.size,\n            height: particle.size,\n            left: particle.x,\n            top: particle.y,\n            opacity: particle.opacity,\n          }}\n          animate={{\n            scale: [1, 1.2, 1],\n            opacity: [particle.opacity, particle.opacity * 0.5, particle.opacity],\n          }}\n          transition={{\n            duration: 3 + Math.random() * 2,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n          }}\n        />\n      ))}\n    </div>\n  )\n}\n\nexport default FloatingParticles\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAeA,MAAM,oBAAoB;IACxB,MAAM,eAAe,IAAA,+MAAM,EAAiB;IAC5C,MAAM,eAAe,IAAA,+MAAM,EAAa,EAAE;IAC1C,MAAM,eAAe,IAAA,+MAAM;IAE3B,IAAA,kNAAS,EAAC;QACR,MAAM,YAAY,aAAa,OAAO;QACtC,IAAI,CAAC,WAAW;QAEhB,uBAAuB;QACvB,MAAM,gBAAgB;QACtB,aAAa,OAAO,GAAG,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAc,GAAG,CAAC,GAAG,IAAM,CAAC;gBACtE,IAAI;gBACJ,GAAG,KAAK,MAAM,KAAK,OAAO,UAAU;gBACpC,GAAG,KAAK,MAAM,KAAK,OAAO,WAAW;gBACrC,MAAM,KAAK,MAAM,KAAK,IAAI;gBAC1B,QAAQ,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAChC,QAAQ,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAChC,SAAS,KAAK,MAAM,KAAK,MAAM;YACjC,CAAC;QAED,MAAM,UAAU;YACd,aAAa,OAAO,CAAC,OAAO,CAAC,CAAA;gBAC3B,SAAS,CAAC,IAAI,SAAS,MAAM;gBAC7B,SAAS,CAAC,IAAI,SAAS,MAAM;gBAE7B,qBAAqB;gBACrB,IAAI,SAAS,CAAC,GAAG,OAAO,UAAU,EAAE,SAAS,CAAC,GAAG;gBACjD,IAAI,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG,OAAO,UAAU;gBAClD,IAAI,SAAS,CAAC,GAAG,OAAO,WAAW,EAAE,SAAS,CAAC,GAAG;gBAClD,IAAI,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG,OAAO,WAAW;YACrD;YAEA,aAAa,OAAO,GAAG,sBAAsB;QAC/C;QAEA;QAEA,OAAO;YACL,IAAI,aAAa,OAAO,EAAE;gBACxB,qBAAqB,aAAa,OAAO;YAC3C;QACF;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,KAAK;QAAc,WAAU;kBAC/B,aAAa,OAAO,CAAC,GAAG,CAAC,CAAA,yBACxB,8OAAC,oMAAM,CAAC,GAAG;gBAET,WAAU;gBACV,OAAO;oBACL,OAAO,SAAS,IAAI;oBACpB,QAAQ,SAAS,IAAI;oBACrB,MAAM,SAAS,CAAC;oBAChB,KAAK,SAAS,CAAC;oBACf,SAAS,SAAS,OAAO;gBAC3B;gBACA,SAAS;oBACP,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;oBAClB,SAAS;wBAAC,SAAS,OAAO;wBAAE,SAAS,OAAO,GAAG;wBAAK,SAAS,OAAO;qBAAC;gBACvE;gBACA,YAAY;oBACV,UAAU,IAAI,KAAK,MAAM,KAAK;oBAC9B,QAAQ;oBACR,MAAM;gBACR;eAjBK,SAAS,EAAE;;;;;;;;;;AAsB1B;uCAEe", "debugId": null}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/telegram-lending%20-2/telegram-shop-landing/src/components/CustomCursor.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { motion } from 'framer-motion'\n\nconst CustomCursor = () => {\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })\n  const [isHovering, setIsHovering] = useState(false)\n\n  useEffect(() => {\n    const updateMousePosition = (e: MouseEvent) => {\n      setMousePosition({ x: e.clientX, y: e.clientY })\n    }\n\n    const handleMouseEnter = () => setIsHovering(true)\n    const handleMouseLeave = () => setIsHovering(false)\n\n    // Add event listeners for interactive elements\n    const interactiveElements = document.querySelectorAll('button, a, [role=\"button\"]')\n    \n    interactiveElements.forEach(el => {\n      el.addEventListener('mouseenter', handleMouseEnter)\n      el.addEventListener('mouseleave', handleMouseLeave)\n    })\n\n    window.addEventListener('mousemove', updateMousePosition)\n\n    return () => {\n      window.removeEventListener('mousemove', updateMousePosition)\n      interactiveElements.forEach(el => {\n        el.removeEventListener('mouseenter', handleMouseEnter)\n        el.removeEventListener('mouseleave', handleMouseLeave)\n      })\n    }\n  }, [])\n\n  return (\n    <>\n      {/* Main cursor */}\n      <motion.div\n        className=\"fixed top-0 left-0 w-4 h-4 bg-blue-500 rounded-full pointer-events-none z-50 mix-blend-difference\"\n        animate={{\n          x: mousePosition.x - 8,\n          y: mousePosition.y - 8,\n          scale: isHovering ? 1.5 : 1,\n        }}\n        transition={{\n          type: \"spring\",\n          stiffness: 500,\n          damping: 28,\n        }}\n      />\n      \n      {/* Cursor trail */}\n      <motion.div\n        className=\"fixed top-0 left-0 w-8 h-8 border-2 border-blue-400/50 rounded-full pointer-events-none z-40\"\n        animate={{\n          x: mousePosition.x - 16,\n          y: mousePosition.y - 16,\n          scale: isHovering ? 2 : 1,\n        }}\n        transition={{\n          type: \"spring\",\n          stiffness: 150,\n          damping: 15,\n        }}\n      />\n    </>\n  )\n}\n\nexport default CustomCursor\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,eAAe;IACnB,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,iNAAQ,EAAC;QAAE,GAAG;QAAG,GAAG;IAAE;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,iNAAQ,EAAC;IAE7C,IAAA,kNAAS,EAAC;QACR,MAAM,sBAAsB,CAAC;YAC3B,iBAAiB;gBAAE,GAAG,EAAE,OAAO;gBAAE,GAAG,EAAE,OAAO;YAAC;QAChD;QAEA,MAAM,mBAAmB,IAAM,cAAc;QAC7C,MAAM,mBAAmB,IAAM,cAAc;QAE7C,+CAA+C;QAC/C,MAAM,sBAAsB,SAAS,gBAAgB,CAAC;QAEtD,oBAAoB,OAAO,CAAC,CAAA;YAC1B,GAAG,gBAAgB,CAAC,cAAc;YAClC,GAAG,gBAAgB,CAAC,cAAc;QACpC;QAEA,OAAO,gBAAgB,CAAC,aAAa;QAErC,OAAO;YACL,OAAO,mBAAmB,CAAC,aAAa;YACxC,oBAAoB,OAAO,CAAC,CAAA;gBAC1B,GAAG,mBAAmB,CAAC,cAAc;gBACrC,GAAG,mBAAmB,CAAC,cAAc;YACvC;QACF;IACF,GAAG,EAAE;IAEL,qBACE;;0BAEE,8OAAC,oMAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG,cAAc,CAAC,GAAG;oBACrB,GAAG,cAAc,CAAC,GAAG;oBACrB,OAAO,aAAa,MAAM;gBAC5B;gBACA,YAAY;oBACV,MAAM;oBACN,WAAW;oBACX,SAAS;gBACX;;;;;;0BAIF,8OAAC,oMAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG,cAAc,CAAC,GAAG;oBACrB,GAAG,cAAc,CAAC,GAAG;oBACrB,OAAO,aAAa,IAAI;gBAC1B;gBACA,YAAY;oBACV,MAAM;oBACN,WAAW;oBACX,SAAS;gBACX;;;;;;;;AAIR;uCAEe", "debugId": null}}]}